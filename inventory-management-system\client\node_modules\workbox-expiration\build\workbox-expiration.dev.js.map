{"version": 3, "file": "workbox-expiration.dev.js", "sources": ["../node_modules/idb/build/wrap-idb-value.js", "../node_modules/idb/build/index.js", "../_version.js", "../models/CacheTimestampsModel.js", "../CacheExpiration.js", "../ExpirationPlugin.js"], "sourcesContent": ["const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction));\n        });\n    }\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking)\n            db.addEventListener('versionchange', () => blocking());\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:expiration:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { openDB, deleteDB } from 'idb';\nimport '../_version.js';\nconst DB_NAME = 'workbox-expiration';\nconst CACHE_OBJECT_STORE = 'cache-entries';\nconst normalizeURL = (unNormalizedUrl) => {\n    const url = new URL(unNormalizedUrl, location.href);\n    url.hash = '';\n    return url.href;\n};\n/**\n * Returns the timestamp model.\n *\n * @private\n */\nclass CacheTimestampsModel {\n    /**\n     *\n     * @param {string} cacheName\n     *\n     * @private\n     */\n    constructor(cacheName) {\n        this._db = null;\n        this._cacheName = cacheName;\n    }\n    /**\n     * Performs an upgrade of indexedDB.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDb(db) {\n        // TODO(philipwalton): EdgeHTML doesn't support arrays as a keyPath, so we\n        // have to use the `id` keyPath here and create our own values (a\n        // concatenation of `url + cacheName`) instead of simply using\n        // `keyPath: ['url', 'cacheName']`, which is supported in other browsers.\n        const objStore = db.createObjectStore(CACHE_OBJECT_STORE, { keyPath: 'id' });\n        // TODO(philipwalton): once we don't have to support EdgeHTML, we can\n        // create a single index with the keyPath `['cacheName', 'timestamp']`\n        // instead of doing both these indexes.\n        objStore.createIndex('cacheName', 'cacheName', { unique: false });\n        objStore.createIndex('timestamp', 'timestamp', { unique: false });\n    }\n    /**\n     * Performs an upgrade of indexedDB and deletes deprecated DBs.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDbAndDeleteOldDbs(db) {\n        this._upgradeDb(db);\n        if (this._cacheName) {\n            void deleteDB(this._cacheName);\n        }\n    }\n    /**\n     * @param {string} url\n     * @param {number} timestamp\n     *\n     * @private\n     */\n    async setTimestamp(url, timestamp) {\n        url = normalizeURL(url);\n        const entry = {\n            url,\n            timestamp,\n            cacheName: this._cacheName,\n            // Creating an ID from the URL and cache name won't be necessary once\n            // Edge switches to Chromium and all browsers we support work with\n            // array keyPaths.\n            id: this._getId(url),\n        };\n        const db = await this.getDb();\n        const tx = db.transaction(CACHE_OBJECT_STORE, 'readwrite', {\n            durability: 'relaxed',\n        });\n        await tx.store.put(entry);\n        await tx.done;\n    }\n    /**\n     * Returns the timestamp stored for a given URL.\n     *\n     * @param {string} url\n     * @return {number | undefined}\n     *\n     * @private\n     */\n    async getTimestamp(url) {\n        const db = await this.getDb();\n        const entry = await db.get(CACHE_OBJECT_STORE, this._getId(url));\n        return entry === null || entry === void 0 ? void 0 : entry.timestamp;\n    }\n    /**\n     * Iterates through all the entries in the object store (from newest to\n     * oldest) and removes entries once either `maxCount` is reached or the\n     * entry's timestamp is less than `minTimestamp`.\n     *\n     * @param {number} minTimestamp\n     * @param {number} maxCount\n     * @return {Array<string>}\n     *\n     * @private\n     */\n    async expireEntries(minTimestamp, maxCount) {\n        const db = await this.getDb();\n        let cursor = await db\n            .transaction(CACHE_OBJECT_STORE)\n            .store.index('timestamp')\n            .openCursor(null, 'prev');\n        const entriesToDelete = [];\n        let entriesNotDeletedCount = 0;\n        while (cursor) {\n            const result = cursor.value;\n            // TODO(philipwalton): once we can use a multi-key index, we\n            // won't have to check `cacheName` here.\n            if (result.cacheName === this._cacheName) {\n                // Delete an entry if it's older than the max age or\n                // if we already have the max number allowed.\n                if ((minTimestamp && result.timestamp < minTimestamp) ||\n                    (maxCount && entriesNotDeletedCount >= maxCount)) {\n                    // TODO(philipwalton): we should be able to delete the\n                    // entry right here, but doing so causes an iteration\n                    // bug in Safari stable (fixed in TP). Instead we can\n                    // store the keys of the entries to delete, and then\n                    // delete the separate transactions.\n                    // https://github.com/GoogleChrome/workbox/issues/1978\n                    // cursor.delete();\n                    // We only need to return the URL, not the whole entry.\n                    entriesToDelete.push(cursor.value);\n                }\n                else {\n                    entriesNotDeletedCount++;\n                }\n            }\n            cursor = await cursor.continue();\n        }\n        // TODO(philipwalton): once the Safari bug in the following issue is fixed,\n        // we should be able to remove this loop and do the entry deletion in the\n        // cursor loop above:\n        // https://github.com/GoogleChrome/workbox/issues/1978\n        const urlsDeleted = [];\n        for (const entry of entriesToDelete) {\n            await db.delete(CACHE_OBJECT_STORE, entry.id);\n            urlsDeleted.push(entry.url);\n        }\n        return urlsDeleted;\n    }\n    /**\n     * Takes a URL and returns an ID that will be unique in the object store.\n     *\n     * @param {string} url\n     * @return {string}\n     *\n     * @private\n     */\n    _getId(url) {\n        // Creating an ID from the URL and cache name won't be necessary once\n        // Edge switches to Chromium and all browsers we support work with\n        // array keyPaths.\n        return this._cacheName + '|' + normalizeURL(url);\n    }\n    /**\n     * Returns an open connection to the database.\n     *\n     * @private\n     */\n    async getDb() {\n        if (!this._db) {\n            this._db = await openDB(DB_NAME, 1, {\n                upgrade: this._upgradeDbAndDeleteOldDbs.bind(this),\n            });\n        }\n        return this._db;\n    }\n}\nexport { CacheTimestampsModel };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheTimestampsModel } from './models/CacheTimestampsModel.js';\nimport './_version.js';\n/**\n * The `CacheExpiration` class allows you define an expiration and / or\n * limit on the number of responses stored in a\n * [`Cache`](https://developer.mozilla.org/en-US/docs/Web/API/Cache).\n *\n * @memberof workbox-expiration\n */\nclass CacheExpiration {\n    /**\n     * To construct a new CacheExpiration instance you must provide at least\n     * one of the `config` properties.\n     *\n     * @param {string} cacheName Name of the cache to apply restrictions to.\n     * @param {Object} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     */\n    constructor(cacheName, config = {}) {\n        this._isRunning = false;\n        this._rerunRequested = false;\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(cacheName, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'constructor',\n                paramName: 'cacheName',\n            });\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._maxEntries = config.maxEntries;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._matchOptions = config.matchOptions;\n        this._cacheName = cacheName;\n        this._timestampModel = new CacheTimestampsModel(cacheName);\n    }\n    /**\n     * Expires entries for the given cache and given criteria.\n     */\n    async expireEntries() {\n        if (this._isRunning) {\n            this._rerunRequested = true;\n            return;\n        }\n        this._isRunning = true;\n        const minTimestamp = this._maxAgeSeconds\n            ? Date.now() - this._maxAgeSeconds * 1000\n            : 0;\n        const urlsExpired = await this._timestampModel.expireEntries(minTimestamp, this._maxEntries);\n        // Delete URLs from the cache\n        const cache = await self.caches.open(this._cacheName);\n        for (const url of urlsExpired) {\n            await cache.delete(url, this._matchOptions);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (urlsExpired.length > 0) {\n                logger.groupCollapsed(`Expired ${urlsExpired.length} ` +\n                    `${urlsExpired.length === 1 ? 'entry' : 'entries'} and removed ` +\n                    `${urlsExpired.length === 1 ? 'it' : 'them'} from the ` +\n                    `'${this._cacheName}' cache.`);\n                logger.log(`Expired the following ${urlsExpired.length === 1 ? 'URL' : 'URLs'}:`);\n                urlsExpired.forEach((url) => logger.log(`    ${url}`));\n                logger.groupEnd();\n            }\n            else {\n                logger.debug(`Cache expiration ran and found no entries to remove.`);\n            }\n        }\n        this._isRunning = false;\n        if (this._rerunRequested) {\n            this._rerunRequested = false;\n            dontWaitFor(this.expireEntries());\n        }\n    }\n    /**\n     * Update the timestamp for the given URL. This ensures the when\n     * removing entries based on maximum entries, most recently used\n     * is accurate or when expiring, the timestamp is up-to-date.\n     *\n     * @param {string} url\n     */\n    async updateTimestamp(url) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(url, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'updateTimestamp',\n                paramName: 'url',\n            });\n        }\n        await this._timestampModel.setTimestamp(url, Date.now());\n    }\n    /**\n     * Can be used to check if a URL has expired or not before it's used.\n     *\n     * This requires a look up from IndexedDB, so can be slow.\n     *\n     * Note: This method will not remove the cached entry, call\n     * `expireEntries()` to remove indexedDB and Cache entries.\n     *\n     * @param {string} url\n     * @return {boolean}\n     */\n    async isURLExpired(url) {\n        if (!this._maxAgeSeconds) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new WorkboxError(`expired-test-without-max-age`, {\n                    methodName: 'isURLExpired',\n                    paramName: 'maxAgeSeconds',\n                });\n            }\n            return false;\n        }\n        else {\n            const timestamp = await this._timestampModel.getTimestamp(url);\n            const expireOlderThan = Date.now() - this._maxAgeSeconds * 1000;\n            return timestamp !== undefined ? timestamp < expireOlderThan : true;\n        }\n    }\n    /**\n     * Removes the IndexedDB object store used to keep track of cache expiration\n     * metadata.\n     */\n    async delete() {\n        // Make sure we don't attempt another rerun if we're called in the middle of\n        // a cache expiration.\n        this._rerunRequested = false;\n        await this._timestampModel.expireEntries(Infinity); // Expires all.\n    }\n}\nexport { CacheExpiration };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { registerQuotaErrorCallback } from 'workbox-core/registerQuotaErrorCallback.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheExpiration } from './CacheExpiration.js';\nimport './_version.js';\n/**\n * This plugin can be used in a `workbox-strategy` to regularly enforce a\n * limit on the age and / or the number of cached requests.\n *\n * It can only be used with `workbox-strategy` instances that have a\n * [custom `cacheName` property set](/web/tools/workbox/guides/configure-workbox#custom_cache_names_in_strategies).\n * In other words, it can't be used to expire entries in strategy that uses the\n * default runtime cache name.\n *\n * Whenever a cached response is used or updated, this plugin will look\n * at the associated cache and remove any old or extra responses.\n *\n * When using `maxAgeSeconds`, responses may be used *once* after expiring\n * because the expiration clean up will not have occurred until *after* the\n * cached response has been used. If the response has a \"Date\" header, then\n * a light weight expiration check is performed and the response will not be\n * used immediately.\n *\n * When using `maxEntries`, the entry least-recently requested will be removed\n * from the cache first.\n *\n * @memberof workbox-expiration\n */\nclass ExpirationPlugin {\n    /**\n     * @param {ExpirationPluginOptions} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     * @param {boolean} [config.purgeOnQuotaError] Whether to opt this cache in to\n     * automatic deletion if the available storage quota has been exceeded.\n     */\n    constructor(config = {}) {\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when a `Response` is about to be returned\n         * from a [Cache](https://developer.mozilla.org/en-US/docs/Web/API/Cache) to\n         * the handler. It allows the `Response` to be inspected for freshness and\n         * prevents it from being used if the `Response`'s `Date` header value is\n         * older than the configured `maxAgeSeconds`.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache the response is in.\n         * @param {Response} options.cachedResponse The `Response` object that's been\n         *     read from a cache and whose freshness should be checked.\n         * @return {Response} Either the `cachedResponse`, if it's\n         *     fresh, or `null` if the `Response` is older than `maxAgeSeconds`.\n         *\n         * @private\n         */\n        this.cachedResponseWillBeUsed = async ({ event, request, cacheName, cachedResponse, }) => {\n            if (!cachedResponse) {\n                return null;\n            }\n            const isFresh = this._isResponseDateFresh(cachedResponse);\n            // Expire entries to ensure that even if the expiration date has\n            // expired, it'll only be used once.\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            dontWaitFor(cacheExpiration.expireEntries());\n            // Update the metadata for the request URL to the current timestamp,\n            // but don't `await` it as we don't want to block the response.\n            const updateTimestampDone = cacheExpiration.updateTimestamp(request.url);\n            if (event) {\n                try {\n                    event.waitUntil(updateTimestampDone);\n                }\n                catch (error) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // The event may not be a fetch event; only log the URL if it is.\n                        if ('request' in event) {\n                            logger.warn(`Unable to ensure service worker stays alive when ` +\n                                `updating cache entry for ` +\n                                `'${getFriendlyURL(event.request.url)}'.`);\n                        }\n                    }\n                }\n            }\n            return isFresh ? cachedResponse : null;\n        };\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when an entry is added to a cache.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache that was updated.\n         * @param {string} options.request The Request for the cached entry.\n         *\n         * @private\n         */\n        this.cacheDidUpdate = async ({ cacheName, request, }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                assert.isType(cacheName, 'string', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'cacheName',\n                });\n                assert.isInstance(request, Request, {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'request',\n                });\n            }\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            await cacheExpiration.updateTimestamp(request.url);\n            await cacheExpiration.expireEntries();\n        };\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._config = config;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._cacheExpirations = new Map();\n        if (config.purgeOnQuotaError) {\n            registerQuotaErrorCallback(() => this.deleteCacheAndMetadata());\n        }\n    }\n    /**\n     * A simple helper method to return a CacheExpiration instance for a given\n     * cache name.\n     *\n     * @param {string} cacheName\n     * @return {CacheExpiration}\n     *\n     * @private\n     */\n    _getCacheExpiration(cacheName) {\n        if (cacheName === cacheNames.getRuntimeName()) {\n            throw new WorkboxError('expire-custom-caches-only');\n        }\n        let cacheExpiration = this._cacheExpirations.get(cacheName);\n        if (!cacheExpiration) {\n            cacheExpiration = new CacheExpiration(cacheName, this._config);\n            this._cacheExpirations.set(cacheName, cacheExpiration);\n        }\n        return cacheExpiration;\n    }\n    /**\n     * @param {Response} cachedResponse\n     * @return {boolean}\n     *\n     * @private\n     */\n    _isResponseDateFresh(cachedResponse) {\n        if (!this._maxAgeSeconds) {\n            // We aren't expiring by age, so return true, it's fresh\n            return true;\n        }\n        // Check if the 'date' header will suffice a quick expiration check.\n        // See https://github.com/GoogleChromeLabs/sw-toolbox/issues/164 for\n        // discussion.\n        const dateHeaderTimestamp = this._getDateHeaderTimestamp(cachedResponse);\n        if (dateHeaderTimestamp === null) {\n            // Unable to parse date, so assume it's fresh.\n            return true;\n        }\n        // If we have a valid headerTime, then our response is fresh iff the\n        // headerTime plus maxAgeSeconds is greater than the current time.\n        const now = Date.now();\n        return dateHeaderTimestamp >= now - this._maxAgeSeconds * 1000;\n    }\n    /**\n     * This method will extract the data header and parse it into a useful\n     * value.\n     *\n     * @param {Response} cachedResponse\n     * @return {number|null}\n     *\n     * @private\n     */\n    _getDateHeaderTimestamp(cachedResponse) {\n        if (!cachedResponse.headers.has('date')) {\n            return null;\n        }\n        const dateHeader = cachedResponse.headers.get('date');\n        const parsedDate = new Date(dateHeader);\n        const headerTime = parsedDate.getTime();\n        // If the Date header was invalid for some reason, parsedDate.getTime()\n        // will return NaN.\n        if (isNaN(headerTime)) {\n            return null;\n        }\n        return headerTime;\n    }\n    /**\n     * This is a helper method that performs two operations:\n     *\n     * - Deletes *all* the underlying Cache instances associated with this plugin\n     * instance, by calling caches.delete() on your behalf.\n     * - Deletes the metadata from IndexedDB used to keep track of expiration\n     * details for each Cache instance.\n     *\n     * When using cache expiration, calling this method is preferable to calling\n     * `caches.delete()` directly, since this will ensure that the IndexedDB\n     * metadata is also cleanly removed and open IndexedDB instances are deleted.\n     *\n     * Note that if you're *not* using cache expiration for a given cache, calling\n     * `caches.delete()` and passing in the cache's name should be sufficient.\n     * There is no Workbox-specific method needed for cleanup in that case.\n     */\n    async deleteCacheAndMetadata() {\n        // Do this one at a time instead of all at once via `Promise.all()` to\n        // reduce the chance of inconsistency if a promise rejects.\n        for (const [cacheName, cacheExpiration] of this._cacheExpirations) {\n            await self.caches.delete(cacheName);\n            await cacheExpiration.delete();\n        }\n        // Reset this._cacheExpirations to its initial state.\n        this._cacheExpirations = new Map();\n    }\n}\nexport { ExpirationPlugin };\n"], "names": ["instanceOfAny", "object", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "getIdbProxyableTypes", "IDBDatabase", "IDBObjectStore", "IDBIndex", "IDBCursor", "IDBTransaction", "getCursorAdvanceMethods", "prototype", "advance", "continue", "continuePrimaryKey", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "promisifyRequest", "request", "promise", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "success", "error", "wrap", "result", "addEventListener", "then", "value", "set", "catch", "cacheDonePromiseForTransaction", "tx", "has", "done", "complete", "DOMException", "idbProxyTraps", "get", "target", "prop", "receiver", "objectStoreNames", "undefined", "objectStore", "replaceTraps", "callback", "wrapFunction", "func", "transaction", "storeNames", "args", "call", "unwrap", "sort", "includes", "apply", "transformCachableValue", "Proxy", "IDBRequest", "newValue", "openDB", "name", "version", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "deleteDB", "deleteDatabase", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "replace", "useIndex", "isWrite", "method", "storeName", "store", "index", "shift", "all", "oldTraps", "self", "_", "e", "DB_NAME", "CACHE_OBJECT_STORE", "normalizeURL", "unNormalizedUrl", "url", "URL", "location", "href", "hash", "CacheTimestampsModel", "constructor", "cacheName", "_db", "_cacheName", "_upgradeDb", "objStore", "createObjectStore", "keyP<PERSON>", "createIndex", "unique", "_upgradeDbAndDeleteOldDbs", "setTimestamp", "timestamp", "entry", "id", "_getId", "getDb", "durability", "put", "getTimestamp", "expireEntries", "minTimestamp", "maxCount", "cursor", "openCursor", "entriesToDelete", "entriesNotDeletedCount", "push", "urlsDeleted", "delete", "bind", "CacheExpiration", "config", "_isRunning", "_rerunRequested", "assert", "isType", "moduleName", "className", "funcName", "paramName", "maxEntries", "maxAgeSeconds", "WorkboxError", "_maxEntries", "_maxAgeSeconds", "_matchOptions", "matchOptions", "_timestampModel", "Date", "now", "urlsExpired", "cache", "caches", "length", "logger", "groupCollapsed", "log", "for<PERSON>ach", "groupEnd", "debug", "dontWait<PERSON>or", "updateTimestamp", "isURLExpired", "methodName", "expire<PERSON><PERSON><PERSON><PERSON>", "Infinity", "ExpirationPlugin", "cachedResponseWillBeUsed", "cachedResponse", "isFresh", "_isResponseDateFresh", "cacheExpiration", "_getCacheExpiration", "updateTimestampDone", "waitUntil", "warn", "getFriendlyURL", "cacheDidUpdate", "isInstance", "Request", "_config", "_cacheExpirations", "purgeOnQuotaError", "registerQuotaErrorCallback", "deleteCacheAndMetadata", "cacheNames", "getRuntimeName", "dateHeaderTimestamp", "_getDateHeaderTimestamp", "headers", "<PERSON><PERSON><PERSON><PERSON>", "parsedDate", "headerTime", "getTime", "isNaN"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;EAAA,MAAMA,aAAa,GAAG,CAACC,MAAD,EAASC,YAAT,KAA0BA,YAAY,CAACC,IAAb,CAAmBC,CAAD,IAAOH,MAAM,YAAYG,CAA3C,CAAhD;;EAEA,IAAIC,iBAAJ;EACA,IAAIC,oBAAJ;;EAEA,SAASC,oBAAT,GAAgC;EAC5B,SAAQF,iBAAiB,KACpBA,iBAAiB,GAAG,CACjBG,WADiB,EAEjBC,cAFiB,EAGjBC,QAHiB,EAIjBC,SAJiB,EAKjBC,cALiB,CADA,CAAzB;EAQH;;;EAED,SAASC,uBAAT,GAAmC;EAC/B,SAAQP,oBAAoB,KACvBA,oBAAoB,GAAG,CACpBK,SAAS,CAACG,SAAV,CAAoBC,OADA,EAEpBJ,SAAS,CAACG,SAAV,CAAoBE,QAFA,EAGpBL,SAAS,CAACG,SAAV,CAAoBG,kBAHA,CADA,CAA5B;EAMH;;EACD,MAAMC,gBAAgB,GAAG,IAAIC,OAAJ,EAAzB;EACA,MAAMC,kBAAkB,GAAG,IAAID,OAAJ,EAA3B;EACA,MAAME,wBAAwB,GAAG,IAAIF,OAAJ,EAAjC;EACA,MAAMG,cAAc,GAAG,IAAIH,OAAJ,EAAvB;EACA,MAAMI,qBAAqB,GAAG,IAAIJ,OAAJ,EAA9B;;EACA,SAASK,gBAAT,CAA0BC,OAA1B,EAAmC;EAC/B,QAAMC,OAAO,GAAG,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;EAC7C,UAAMC,QAAQ,GAAG,MAAM;EACnBL,MAAAA,OAAO,CAACM,mBAAR,CAA4B,SAA5B,EAAuCC,OAAvC;EACAP,MAAAA,OAAO,CAACM,mBAAR,CAA4B,OAA5B,EAAqCE,KAArC;EACH,KAHD;;EAIA,UAAMD,OAAO,GAAG,MAAM;EAClBJ,MAAAA,OAAO,CAACM,IAAI,CAACT,OAAO,CAACU,MAAT,CAAL,CAAP;EACAL,MAAAA,QAAQ;EACX,KAHD;;EAIA,UAAMG,KAAK,GAAG,MAAM;EAChBJ,MAAAA,MAAM,CAACJ,OAAO,CAACQ,KAAT,CAAN;EACAH,MAAAA,QAAQ;EACX,KAHD;;EAIAL,IAAAA,OAAO,CAACW,gBAAR,CAAyB,SAAzB,EAAoCJ,OAApC;EACAP,IAAAA,OAAO,CAACW,gBAAR,CAAyB,OAAzB,EAAkCH,KAAlC;EACH,GAfe,CAAhB;EAgBAP,EAAAA,OAAO,CACFW,IADL,CACWC,KAAD,IAAW;EACjB;EACA;EACA,QAAIA,KAAK,YAAY3B,SAArB,EAAgC;EAC5BO,MAAAA,gBAAgB,CAACqB,GAAjB,CAAqBD,KAArB,EAA4Bb,OAA5B;EACH,KALgB;;EAOpB,GARD,EASKe,KATL,CASW,MAAM,EATjB,EAjB+B;EA4B/B;;EACAjB,EAAAA,qBAAqB,CAACgB,GAAtB,CAA0Bb,OAA1B,EAAmCD,OAAnC;EACA,SAAOC,OAAP;EACH;;EACD,SAASe,8BAAT,CAAwCC,EAAxC,EAA4C;EACxC;EACA,MAAItB,kBAAkB,CAACuB,GAAnB,CAAuBD,EAAvB,CAAJ,EACI;EACJ,QAAME,IAAI,GAAG,IAAIjB,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;EAC1C,UAAMC,QAAQ,GAAG,MAAM;EACnBY,MAAAA,EAAE,CAACX,mBAAH,CAAuB,UAAvB,EAAmCc,QAAnC;EACAH,MAAAA,EAAE,CAACX,mBAAH,CAAuB,OAAvB,EAAgCE,KAAhC;EACAS,MAAAA,EAAE,CAACX,mBAAH,CAAuB,OAAvB,EAAgCE,KAAhC;EACH,KAJD;;EAKA,UAAMY,QAAQ,GAAG,MAAM;EACnBjB,MAAAA,OAAO;EACPE,MAAAA,QAAQ;EACX,KAHD;;EAIA,UAAMG,KAAK,GAAG,MAAM;EAChBJ,MAAAA,MAAM,CAACa,EAAE,CAACT,KAAH,IAAY,IAAIa,YAAJ,CAAiB,YAAjB,EAA+B,YAA/B,CAAb,CAAN;EACAhB,MAAAA,QAAQ;EACX,KAHD;;EAIAY,IAAAA,EAAE,CAACN,gBAAH,CAAoB,UAApB,EAAgCS,QAAhC;EACAH,IAAAA,EAAE,CAACN,gBAAH,CAAoB,OAApB,EAA6BH,KAA7B;EACAS,IAAAA,EAAE,CAACN,gBAAH,CAAoB,OAApB,EAA6BH,KAA7B;EACH,GAjBY,CAAb,CAJwC;;EAuBxCb,EAAAA,kBAAkB,CAACmB,GAAnB,CAAuBG,EAAvB,EAA2BE,IAA3B;EACH;;EACD,IAAIG,aAAa,GAAG;EAChBC,EAAAA,GAAG,CAACC,MAAD,EAASC,IAAT,EAAeC,QAAf,EAAyB;EACxB,QAAIF,MAAM,YAAYrC,cAAtB,EAAsC;EAClC;EACA,UAAIsC,IAAI,KAAK,MAAb,EACI,OAAO9B,kBAAkB,CAAC4B,GAAnB,CAAuBC,MAAvB,CAAP,CAH8B;;EAKlC,UAAIC,IAAI,KAAK,kBAAb,EAAiC;EAC7B,eAAOD,MAAM,CAACG,gBAAP,IAA2B/B,wBAAwB,CAAC2B,GAAzB,CAA6BC,MAA7B,CAAlC;EACH,OAPiC;;;EASlC,UAAIC,IAAI,KAAK,OAAb,EAAsB;EAClB,eAAOC,QAAQ,CAACC,gBAAT,CAA0B,CAA1B,IACDC,SADC,GAEDF,QAAQ,CAACG,WAAT,CAAqBH,QAAQ,CAACC,gBAAT,CAA0B,CAA1B,CAArB,CAFN;EAGH;EACJ,KAfuB;;;EAiBxB,WAAOlB,IAAI,CAACe,MAAM,CAACC,IAAD,CAAP,CAAX;EACH,GAnBe;;EAoBhBX,EAAAA,GAAG,CAACU,MAAD,EAASC,IAAT,EAAeZ,KAAf,EAAsB;EACrBW,IAAAA,MAAM,CAACC,IAAD,CAAN,GAAeZ,KAAf;EACA,WAAO,IAAP;EACH,GAvBe;;EAwBhBK,EAAAA,GAAG,CAACM,MAAD,EAASC,IAAT,EAAe;EACd,QAAID,MAAM,YAAYrC,cAAlB,KACCsC,IAAI,KAAK,MAAT,IAAmBA,IAAI,KAAK,OAD7B,CAAJ,EAC2C;EACvC,aAAO,IAAP;EACH;;EACD,WAAOA,IAAI,IAAID,MAAf;EACH;;EA9Be,CAApB;;EAgCA,SAASM,YAAT,CAAsBC,QAAtB,EAAgC;EAC5BT,EAAAA,aAAa,GAAGS,QAAQ,CAACT,aAAD,CAAxB;EACH;;EACD,SAASU,YAAT,CAAsBC,IAAtB,EAA4B;EACxB;EACA;EACA;EACA,MAAIA,IAAI,KAAKlD,WAAW,CAACM,SAAZ,CAAsB6C,WAA/B,IACA,EAAE,sBAAsB/C,cAAc,CAACE,SAAvC,CADJ,EACuD;EACnD,WAAO,UAAU8C,UAAV,EAAsB,GAAGC,IAAzB,EAA+B;EAClC,YAAMnB,EAAE,GAAGgB,IAAI,CAACI,IAAL,CAAUC,MAAM,CAAC,IAAD,CAAhB,EAAwBH,UAAxB,EAAoC,GAAGC,IAAvC,CAAX;EACAxC,MAAAA,wBAAwB,CAACkB,GAAzB,CAA6BG,EAA7B,EAAiCkB,UAAU,CAACI,IAAX,GAAkBJ,UAAU,CAACI,IAAX,EAAlB,GAAsC,CAACJ,UAAD,CAAvE;EACA,aAAO1B,IAAI,CAACQ,EAAD,CAAX;EACH,KAJD;EAKH,GAXuB;EAaxB;EACA;EACA;EACA;;;EACA,MAAI7B,uBAAuB,GAAGoD,QAA1B,CAAmCP,IAAnC,CAAJ,EAA8C;EAC1C,WAAO,UAAU,GAAGG,IAAb,EAAmB;EACtB;EACA;EACAH,MAAAA,IAAI,CAACQ,KAAL,CAAWH,MAAM,CAAC,IAAD,CAAjB,EAAyBF,IAAzB;EACA,aAAO3B,IAAI,CAAChB,gBAAgB,CAAC8B,GAAjB,CAAqB,IAArB,CAAD,CAAX;EACH,KALD;EAMH;;EACD,SAAO,UAAU,GAAGa,IAAb,EAAmB;EACtB;EACA;EACA,WAAO3B,IAAI,CAACwB,IAAI,CAACQ,KAAL,CAAWH,MAAM,CAAC,IAAD,CAAjB,EAAyBF,IAAzB,CAAD,CAAX;EACH,GAJD;EAKH;;EACD,SAASM,sBAAT,CAAgC7B,KAAhC,EAAuC;EACnC,MAAI,OAAOA,KAAP,KAAiB,UAArB,EACI,OAAOmB,YAAY,CAACnB,KAAD,CAAnB,CAF+B;EAInC;;EACA,MAAIA,KAAK,YAAY1B,cAArB,EACI6B,8BAA8B,CAACH,KAAD,CAA9B;EACJ,MAAItC,aAAa,CAACsC,KAAD,EAAQ/B,oBAAoB,EAA5B,CAAjB,EACI,OAAO,IAAI6D,KAAJ,CAAU9B,KAAV,EAAiBS,aAAjB,CAAP,CAR+B;;EAUnC,SAAOT,KAAP;EACH;;EACD,SAASJ,IAAT,CAAcI,KAAd,EAAqB;EACjB;EACA;EACA,MAAIA,KAAK,YAAY+B,UAArB,EACI,OAAO7C,gBAAgB,CAACc,KAAD,CAAvB,CAJa;EAMjB;;EACA,MAAIhB,cAAc,CAACqB,GAAf,CAAmBL,KAAnB,CAAJ,EACI,OAAOhB,cAAc,CAAC0B,GAAf,CAAmBV,KAAnB,CAAP;EACJ,QAAMgC,QAAQ,GAAGH,sBAAsB,CAAC7B,KAAD,CAAvC,CATiB;EAWjB;;EACA,MAAIgC,QAAQ,KAAKhC,KAAjB,EAAwB;EACpBhB,IAAAA,cAAc,CAACiB,GAAf,CAAmBD,KAAnB,EAA0BgC,QAA1B;EACA/C,IAAAA,qBAAqB,CAACgB,GAAtB,CAA0B+B,QAA1B,EAAoChC,KAApC;EACH;;EACD,SAAOgC,QAAP;EACH;;EACD,MAAMP,MAAM,GAAIzB,KAAD,IAAWf,qBAAqB,CAACyB,GAAtB,CAA0BV,KAA1B,CAA1B;;ECnLA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,SAASiC,MAAT,CAAgBC,IAAhB,EAAsBC,OAAtB,EAA+B;EAAEC,EAAAA,OAAF;EAAWC,EAAAA,OAAX;EAAoBC,EAAAA,QAApB;EAA8BC,EAAAA;EAA9B,IAA6C,EAA5E,EAAgF;EAC5E,QAAMpD,OAAO,GAAGqD,SAAS,CAACC,IAAV,CAAeP,IAAf,EAAqBC,OAArB,CAAhB;EACA,QAAMO,WAAW,GAAG9C,IAAI,CAACT,OAAD,CAAxB;;EACA,MAAIkD,OAAJ,EAAa;EACTlD,IAAAA,OAAO,CAACW,gBAAR,CAAyB,eAAzB,EAA2C6C,KAAD,IAAW;EACjDN,MAAAA,OAAO,CAACzC,IAAI,CAACT,OAAO,CAACU,MAAT,CAAL,EAAuB8C,KAAK,CAACC,UAA7B,EAAyCD,KAAK,CAACE,UAA/C,EAA2DjD,IAAI,CAACT,OAAO,CAACkC,WAAT,CAA/D,CAAP;EACH,KAFD;EAGH;;EACD,MAAIe,OAAJ,EACIjD,OAAO,CAACW,gBAAR,CAAyB,SAAzB,EAAoC,MAAMsC,OAAO,EAAjD;EACJM,EAAAA,WAAW,CACN3C,IADL,CACW+C,EAAD,IAAQ;EACd,QAAIP,UAAJ,EACIO,EAAE,CAAChD,gBAAH,CAAoB,OAApB,EAA6B,MAAMyC,UAAU,EAA7C;EACJ,QAAID,QAAJ,EACIQ,EAAE,CAAChD,gBAAH,CAAoB,eAApB,EAAqC,MAAMwC,QAAQ,EAAnD;EACP,GAND,EAOKpC,KAPL,CAOW,MAAM,EAPjB;EAQA,SAAOwC,WAAP;EACH;EACD;EACA;EACA;EACA;EACA;;;EACA,SAASK,QAAT,CAAkBb,IAAlB,EAAwB;EAAEE,EAAAA;EAAF,IAAc,EAAtC,EAA0C;EACtC,QAAMjD,OAAO,GAAGqD,SAAS,CAACQ,cAAV,CAAyBd,IAAzB,CAAhB;EACA,MAAIE,OAAJ,EACIjD,OAAO,CAACW,gBAAR,CAAyB,SAAzB,EAAoC,MAAMsC,OAAO,EAAjD;EACJ,SAAOxC,IAAI,CAACT,OAAD,CAAJ,CAAcY,IAAd,CAAmB,MAAMgB,SAAzB,CAAP;EACH;;EAED,MAAMkC,WAAW,GAAG,CAAC,KAAD,EAAQ,QAAR,EAAkB,QAAlB,EAA4B,YAA5B,EAA0C,OAA1C,CAApB;EACA,MAAMC,YAAY,GAAG,CAAC,KAAD,EAAQ,KAAR,EAAe,QAAf,EAAyB,OAAzB,CAArB;EACA,MAAMC,aAAa,GAAG,IAAIC,GAAJ,EAAtB;;EACA,SAASC,SAAT,CAAmB1C,MAAnB,EAA2BC,IAA3B,EAAiC;EAC7B,MAAI,EAAED,MAAM,YAAYzC,WAAlB,IACF,EAAE0C,IAAI,IAAID,MAAV,CADE,IAEF,OAAOC,IAAP,KAAgB,QAFhB,CAAJ,EAE+B;EAC3B;EACH;;EACD,MAAIuC,aAAa,CAACzC,GAAd,CAAkBE,IAAlB,CAAJ,EACI,OAAOuC,aAAa,CAACzC,GAAd,CAAkBE,IAAlB,CAAP;EACJ,QAAM0C,cAAc,GAAG1C,IAAI,CAAC2C,OAAL,CAAa,YAAb,EAA2B,EAA3B,CAAvB;EACA,QAAMC,QAAQ,GAAG5C,IAAI,KAAK0C,cAA1B;EACA,QAAMG,OAAO,GAAGP,YAAY,CAACvB,QAAb,CAAsB2B,cAAtB,CAAhB;;EACA;EAEA,IAAEA,cAAc,IAAI,CAACE,QAAQ,GAAGpF,QAAH,GAAcD,cAAvB,EAAuCK,SAA3D,KACI,EAAEiF,OAAO,IAAIR,WAAW,CAACtB,QAAZ,CAAqB2B,cAArB,CAAb,CAHJ,EAGwD;EACpD;EACH;;EACD,QAAMI,MAAM,GAAG,gBAAgBC,SAAhB,EAA2B,GAAGpC,IAA9B,EAAoC;EAC/C;EACA,UAAMnB,EAAE,GAAG,KAAKiB,WAAL,CAAiBsC,SAAjB,EAA4BF,OAAO,GAAG,WAAH,GAAiB,UAApD,CAAX;EACA,QAAI9C,MAAM,GAAGP,EAAE,CAACwD,KAAhB;EACA,QAAIJ,QAAJ,EACI7C,MAAM,GAAGA,MAAM,CAACkD,KAAP,CAAatC,IAAI,CAACuC,KAAL,EAAb,CAAT,CAL2C;EAO/C;EACA;EACA;EACA;;EACA,WAAO,CAAC,MAAMzE,OAAO,CAAC0E,GAAR,CAAY,CACtBpD,MAAM,CAAC2C,cAAD,CAAN,CAAuB,GAAG/B,IAA1B,CADsB,EAEtBkC,OAAO,IAAIrD,EAAE,CAACE,IAFQ,CAAZ,CAAP,EAGH,CAHG,CAAP;EAIH,GAfD;;EAgBA6C,EAAAA,aAAa,CAAClD,GAAd,CAAkBW,IAAlB,EAAwB8C,MAAxB;EACA,SAAOA,MAAP;EACH;;EACDzC,YAAY,CAAE+C,QAAD,iBACNA,QADM;EAETtD,EAAAA,GAAG,EAAE,CAACC,MAAD,EAASC,IAAT,EAAeC,QAAf,KAA4BwC,SAAS,CAAC1C,MAAD,EAASC,IAAT,CAAT,IAA2BoD,QAAQ,CAACtD,GAAT,CAAaC,MAAb,EAAqBC,IAArB,EAA2BC,QAA3B,CAFnD;EAGTR,EAAAA,GAAG,EAAE,CAACM,MAAD,EAASC,IAAT,KAAkB,CAAC,CAACyC,SAAS,CAAC1C,MAAD,EAASC,IAAT,CAAX,IAA6BoD,QAAQ,CAAC3D,GAAT,CAAaM,MAAb,EAAqBC,IAArB;EAH3C,EAAD,CAAZ;;EC/EA,IAAI;EACAqD,EAAAA,IAAI,CAAC,0BAAD,CAAJ,IAAoCC,CAAC,EAArC;EACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ECLV;EACA;AACA;EACA;EACA;EACA;EACA;EAGA,MAAMC,OAAO,GAAG,oBAAhB;EACA,MAAMC,kBAAkB,GAAG,eAA3B;;EACA,MAAMC,YAAY,GAAIC,eAAD,IAAqB;EACtC,QAAMC,GAAG,GAAG,IAAIC,GAAJ,CAAQF,eAAR,EAAyBG,QAAQ,CAACC,IAAlC,CAAZ;EACAH,EAAAA,GAAG,CAACI,IAAJ,GAAW,EAAX;EACA,SAAOJ,GAAG,CAACG,IAAX;EACH,CAJD;EAKA;EACA;EACA;EACA;EACA;;;EACA,MAAME,oBAAN,CAA2B;EACvB;EACJ;EACA;EACA;EACA;EACA;EACIC,EAAAA,WAAW,CAACC,SAAD,EAAY;EACnB,SAAKC,GAAL,GAAW,IAAX;EACA,SAAKC,UAAL,GAAkBF,SAAlB;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EACIG,EAAAA,UAAU,CAACpC,EAAD,EAAK;EACX;EACA;EACA;EACA;EACA,UAAMqC,QAAQ,GAAGrC,EAAE,CAACsC,iBAAH,CAAqBf,kBAArB,EAAyC;EAAEgB,MAAAA,OAAO,EAAE;EAAX,KAAzC,CAAjB,CALW;EAOX;EACA;;EACAF,IAAAA,QAAQ,CAACG,WAAT,CAAqB,WAArB,EAAkC,WAAlC,EAA+C;EAAEC,MAAAA,MAAM,EAAE;EAAV,KAA/C;EACAJ,IAAAA,QAAQ,CAACG,WAAT,CAAqB,WAArB,EAAkC,WAAlC,EAA+C;EAAEC,MAAAA,MAAM,EAAE;EAAV,KAA/C;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EACIC,EAAAA,yBAAyB,CAAC1C,EAAD,EAAK;EAC1B,SAAKoC,UAAL,CAAgBpC,EAAhB;;EACA,QAAI,KAAKmC,UAAT,EAAqB;EACjB,WAAKlC,QAAQ,CAAC,KAAKkC,UAAN,CAAb;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;;;EACI,QAAMQ,YAAN,CAAmBjB,GAAnB,EAAwBkB,SAAxB,EAAmC;EAC/BlB,IAAAA,GAAG,GAAGF,YAAY,CAACE,GAAD,CAAlB;EACA,UAAMmB,KAAK,GAAG;EACVnB,MAAAA,GADU;EAEVkB,MAAAA,SAFU;EAGVX,MAAAA,SAAS,EAAE,KAAKE,UAHN;EAIV;EACA;EACA;EACAW,MAAAA,EAAE,EAAE,KAAKC,MAAL,CAAYrB,GAAZ;EAPM,KAAd;EASA,UAAM1B,EAAE,GAAG,MAAM,KAAKgD,KAAL,EAAjB;EACA,UAAM1F,EAAE,GAAG0C,EAAE,CAACzB,WAAH,CAAegD,kBAAf,EAAmC,WAAnC,EAAgD;EACvD0B,MAAAA,UAAU,EAAE;EAD2C,KAAhD,CAAX;EAGA,UAAM3F,EAAE,CAACwD,KAAH,CAASoC,GAAT,CAAaL,KAAb,CAAN;EACA,UAAMvF,EAAE,CAACE,IAAT;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAM2F,YAAN,CAAmBzB,GAAnB,EAAwB;EACpB,UAAM1B,EAAE,GAAG,MAAM,KAAKgD,KAAL,EAAjB;EACA,UAAMH,KAAK,GAAG,MAAM7C,EAAE,CAACpC,GAAH,CAAO2D,kBAAP,EAA2B,KAAKwB,MAAL,CAAYrB,GAAZ,CAA3B,CAApB;EACA,WAAOmB,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACD,SAA3D;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMQ,aAAN,CAAoBC,YAApB,EAAkCC,QAAlC,EAA4C;EACxC,UAAMtD,EAAE,GAAG,MAAM,KAAKgD,KAAL,EAAjB;EACA,QAAIO,MAAM,GAAG,MAAMvD,EAAE,CAChBzB,WADc,CACFgD,kBADE,EAEdT,KAFc,CAERC,KAFQ,CAEF,WAFE,EAGdyC,UAHc,CAGH,IAHG,EAGG,MAHH,CAAnB;EAIA,UAAMC,eAAe,GAAG,EAAxB;EACA,QAAIC,sBAAsB,GAAG,CAA7B;;EACA,WAAOH,MAAP,EAAe;EACX,YAAMxG,MAAM,GAAGwG,MAAM,CAACrG,KAAtB,CADW;EAGX;;EACA,UAAIH,MAAM,CAACkF,SAAP,KAAqB,KAAKE,UAA9B,EAA0C;EACtC;EACA;EACA,YAAKkB,YAAY,IAAItG,MAAM,CAAC6F,SAAP,GAAmBS,YAApC,IACCC,QAAQ,IAAII,sBAAsB,IAAIJ,QAD3C,EACsD;EAClD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAG,UAAAA,eAAe,CAACE,IAAhB,CAAqBJ,MAAM,CAACrG,KAA5B;EACH,SAXD,MAYK;EACDwG,UAAAA,sBAAsB;EACzB;EACJ;;EACDH,MAAAA,MAAM,GAAG,MAAMA,MAAM,CAAC3H,QAAP,EAAf;EACH,KAhCuC;EAkCxC;EACA;EACA;;;EACA,UAAMgI,WAAW,GAAG,EAApB;;EACA,SAAK,MAAMf,KAAX,IAAoBY,eAApB,EAAqC;EACjC,YAAMzD,EAAE,CAAC6D,MAAH,CAAUtC,kBAAV,EAA8BsB,KAAK,CAACC,EAApC,CAAN;EACAc,MAAAA,WAAW,CAACD,IAAZ,CAAiBd,KAAK,CAACnB,GAAvB;EACH;;EACD,WAAOkC,WAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;;EACIb,EAAAA,MAAM,CAACrB,GAAD,EAAM;EACR;EACA;EACA;EACA,WAAO,KAAKS,UAAL,GAAkB,GAAlB,GAAwBX,YAAY,CAACE,GAAD,CAA3C;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAMsB,KAAN,GAAc;EACV,QAAI,CAAC,KAAKd,GAAV,EAAe;EACX,WAAKA,GAAL,GAAW,MAAM/C,MAAM,CAACmC,OAAD,EAAU,CAAV,EAAa;EAChC/B,QAAAA,OAAO,EAAE,KAAKmD,yBAAL,CAA+BoB,IAA/B,CAAoC,IAApC;EADuB,OAAb,CAAvB;EAGH;;EACD,WAAO,KAAK5B,GAAZ;EACH;;EAjKsB;;ECrB3B;EACA;AACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,MAAM6B,eAAN,CAAsB;EAClB;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI/B,EAAAA,WAAW,CAACC,SAAD,EAAY+B,MAAM,GAAG,EAArB,EAAyB;EAChC,SAAKC,UAAL,GAAkB,KAAlB;EACA,SAAKC,eAAL,GAAuB,KAAvB;;EACA,IAA2C;EACvCC,MAAAA,gBAAM,CAACC,MAAP,CAAcnC,SAAd,EAAyB,QAAzB,EAAmC;EAC/BoC,QAAAA,UAAU,EAAE,oBADmB;EAE/BC,QAAAA,SAAS,EAAE,iBAFoB;EAG/BC,QAAAA,QAAQ,EAAE,aAHqB;EAI/BC,QAAAA,SAAS,EAAE;EAJoB,OAAnC;;EAMA,UAAI,EAAER,MAAM,CAACS,UAAP,IAAqBT,MAAM,CAACU,aAA9B,CAAJ,EAAkD;EAC9C,cAAM,IAAIC,4BAAJ,CAAiB,6BAAjB,EAAgD;EAClDN,UAAAA,UAAU,EAAE,oBADsC;EAElDC,UAAAA,SAAS,EAAE,iBAFuC;EAGlDC,UAAAA,QAAQ,EAAE;EAHwC,SAAhD,CAAN;EAKH;;EACD,UAAIP,MAAM,CAACS,UAAX,EAAuB;EACnBN,QAAAA,gBAAM,CAACC,MAAP,CAAcJ,MAAM,CAACS,UAArB,EAAiC,QAAjC,EAA2C;EACvCJ,UAAAA,UAAU,EAAE,oBAD2B;EAEvCC,UAAAA,SAAS,EAAE,iBAF4B;EAGvCC,UAAAA,QAAQ,EAAE,aAH6B;EAIvCC,UAAAA,SAAS,EAAE;EAJ4B,SAA3C;EAMH;;EACD,UAAIR,MAAM,CAACU,aAAX,EAA0B;EACtBP,QAAAA,gBAAM,CAACC,MAAP,CAAcJ,MAAM,CAACU,aAArB,EAAoC,QAApC,EAA8C;EAC1CL,UAAAA,UAAU,EAAE,oBAD8B;EAE1CC,UAAAA,SAAS,EAAE,iBAF+B;EAG1CC,UAAAA,QAAQ,EAAE,aAHgC;EAI1CC,UAAAA,SAAS,EAAE;EAJ+B,SAA9C;EAMH;EACJ;;EACD,SAAKI,WAAL,GAAmBZ,MAAM,CAACS,UAA1B;EACA,SAAKI,cAAL,GAAsBb,MAAM,CAACU,aAA7B;EACA,SAAKI,aAAL,GAAqBd,MAAM,CAACe,YAA5B;EACA,SAAK5C,UAAL,GAAkBF,SAAlB;EACA,SAAK+C,eAAL,GAAuB,IAAIjD,oBAAJ,CAAyBE,SAAzB,CAAvB;EACH;EACD;EACJ;EACA;;;EACI,QAAMmB,aAAN,GAAsB;EAClB,QAAI,KAAKa,UAAT,EAAqB;EACjB,WAAKC,eAAL,GAAuB,IAAvB;EACA;EACH;;EACD,SAAKD,UAAL,GAAkB,IAAlB;EACA,UAAMZ,YAAY,GAAG,KAAKwB,cAAL,GACfI,IAAI,CAACC,GAAL,KAAa,KAAKL,cAAL,GAAsB,IADpB,GAEf,CAFN;EAGA,UAAMM,WAAW,GAAG,MAAM,KAAKH,eAAL,CAAqB5B,aAArB,CAAmCC,YAAnC,EAAiD,KAAKuB,WAAtD,CAA1B,CATkB;;EAWlB,UAAMQ,KAAK,GAAG,MAAMjE,IAAI,CAACkE,MAAL,CAAY1F,IAAZ,CAAiB,KAAKwC,UAAtB,CAApB;;EACA,SAAK,MAAMT,GAAX,IAAkByD,WAAlB,EAA+B;EAC3B,YAAMC,KAAK,CAACvB,MAAN,CAAanC,GAAb,EAAkB,KAAKoD,aAAvB,CAAN;EACH;;EACD,IAA2C;EACvC,UAAIK,WAAW,CAACG,MAAZ,GAAqB,CAAzB,EAA4B;EACxBC,QAAAA,gBAAM,CAACC,cAAP,CAAuB,WAAUL,WAAW,CAACG,MAAO,GAA9B,GACjB,GAAEH,WAAW,CAACG,MAAZ,KAAuB,CAAvB,GAA2B,OAA3B,GAAqC,SAAU,eADhC,GAEjB,GAAEH,WAAW,CAACG,MAAZ,KAAuB,CAAvB,GAA2B,IAA3B,GAAkC,MAAO,YAF1B,GAGjB,IAAG,KAAKnD,UAAW,UAHxB;EAIAoD,QAAAA,gBAAM,CAACE,GAAP,CAAY,yBAAwBN,WAAW,CAACG,MAAZ,KAAuB,CAAvB,GAA2B,KAA3B,GAAmC,MAAO,GAA9E;EACAH,QAAAA,WAAW,CAACO,OAAZ,CAAqBhE,GAAD,IAAS6D,gBAAM,CAACE,GAAP,CAAY,OAAM/D,GAAI,EAAtB,CAA7B;EACA6D,QAAAA,gBAAM,CAACI,QAAP;EACH,OARD,MASK;EACDJ,QAAAA,gBAAM,CAACK,KAAP,CAAc,sDAAd;EACH;EACJ;;EACD,SAAK3B,UAAL,GAAkB,KAAlB;;EACA,QAAI,KAAKC,eAAT,EAA0B;EACtB,WAAKA,eAAL,GAAuB,KAAvB;EACA2B,MAAAA,0BAAW,CAAC,KAAKzC,aAAL,EAAD,CAAX;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAM0C,eAAN,CAAsBpE,GAAtB,EAA2B;EACvB,IAA2C;EACvCyC,MAAAA,gBAAM,CAACC,MAAP,CAAc1C,GAAd,EAAmB,QAAnB,EAA6B;EACzB2C,QAAAA,UAAU,EAAE,oBADa;EAEzBC,QAAAA,SAAS,EAAE,iBAFc;EAGzBC,QAAAA,QAAQ,EAAE,iBAHe;EAIzBC,QAAAA,SAAS,EAAE;EAJc,OAA7B;EAMH;;EACD,UAAM,KAAKQ,eAAL,CAAqBrC,YAArB,CAAkCjB,GAAlC,EAAuCuD,IAAI,CAACC,GAAL,EAAvC,CAAN;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMa,YAAN,CAAmBrE,GAAnB,EAAwB;EACpB,QAAI,CAAC,KAAKmD,cAAV,EAA0B;EACtB,MAA2C;EACvC,cAAM,IAAIF,4BAAJ,CAAkB,8BAAlB,EAAiD;EACnDqB,UAAAA,UAAU,EAAE,cADuC;EAEnDxB,UAAAA,SAAS,EAAE;EAFwC,SAAjD,CAAN;EAIH;EAEJ,KARD,MASK;EACD,YAAM5B,SAAS,GAAG,MAAM,KAAKoC,eAAL,CAAqB7B,YAArB,CAAkCzB,GAAlC,CAAxB;EACA,YAAMuE,eAAe,GAAGhB,IAAI,CAACC,GAAL,KAAa,KAAKL,cAAL,GAAsB,IAA3D;EACA,aAAOjC,SAAS,KAAK3E,SAAd,GAA0B2E,SAAS,GAAGqD,eAAtC,GAAwD,IAA/D;EACH;EACJ;EACD;EACJ;EACA;EACA;;;EACI,QAAMpC,MAAN,GAAe;EACX;EACA;EACA,SAAKK,eAAL,GAAuB,KAAvB;EACA,UAAM,KAAKc,eAAL,CAAqB5B,aAArB,CAAmC8C,QAAnC,CAAN,CAJW;EAKd;;EAlJiB;;ECpBtB;EACA;AACA;EACA;EACA;EACA;EACA;EAUA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAN,CAAuB;EACnB;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACInE,EAAAA,WAAW,CAACgC,MAAM,GAAG,EAAV,EAAc;EACrB;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACQ,SAAKoC,wBAAL,GAAgC,OAAO;EAAEvG,MAAAA,KAAF;EAASxD,MAAAA,OAAT;EAAkB4F,MAAAA,SAAlB;EAA6BoE,MAAAA;EAA7B,KAAP,KAA0D;EACtF,UAAI,CAACA,cAAL,EAAqB;EACjB,eAAO,IAAP;EACH;;EACD,YAAMC,OAAO,GAAG,KAAKC,oBAAL,CAA0BF,cAA1B,CAAhB,CAJsF;EAMtF;;;EACA,YAAMG,eAAe,GAAG,KAAKC,mBAAL,CAAyBxE,SAAzB,CAAxB;;EACA4D,MAAAA,0BAAW,CAACW,eAAe,CAACpD,aAAhB,EAAD,CAAX,CARsF;EAUtF;;EACA,YAAMsD,mBAAmB,GAAGF,eAAe,CAACV,eAAhB,CAAgCzJ,OAAO,CAACqF,GAAxC,CAA5B;;EACA,UAAI7B,KAAJ,EAAW;EACP,YAAI;EACAA,UAAAA,KAAK,CAAC8G,SAAN,CAAgBD,mBAAhB;EACH,SAFD,CAGA,OAAO7J,KAAP,EAAc;EACV,UAA2C;EACvC;EACA,gBAAI,aAAagD,KAAjB,EAAwB;EACpB0F,cAAAA,gBAAM,CAACqB,IAAP,CAAa,mDAAD,GACP,2BADO,GAEP,IAAGC,gCAAc,CAAChH,KAAK,CAACxD,OAAN,CAAcqF,GAAf,CAAoB,IAF1C;EAGH;EACJ;EACJ;EACJ;;EACD,aAAO4E,OAAO,GAAGD,cAAH,GAAoB,IAAlC;EACH,KA5BD;EA6BA;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACQ,SAAKS,cAAL,GAAsB,OAAO;EAAE7E,MAAAA,SAAF;EAAa5F,MAAAA;EAAb,KAAP,KAAmC;EACrD,MAA2C;EACvC8H,QAAAA,gBAAM,CAACC,MAAP,CAAcnC,SAAd,EAAyB,QAAzB,EAAmC;EAC/BoC,UAAAA,UAAU,EAAE,oBADmB;EAE/BC,UAAAA,SAAS,EAAE,QAFoB;EAG/BC,UAAAA,QAAQ,EAAE,gBAHqB;EAI/BC,UAAAA,SAAS,EAAE;EAJoB,SAAnC;EAMAL,QAAAA,gBAAM,CAAC4C,UAAP,CAAkB1K,OAAlB,EAA2B2K,OAA3B,EAAoC;EAChC3C,UAAAA,UAAU,EAAE,oBADoB;EAEhCC,UAAAA,SAAS,EAAE,QAFqB;EAGhCC,UAAAA,QAAQ,EAAE,gBAHsB;EAIhCC,UAAAA,SAAS,EAAE;EAJqB,SAApC;EAMH;;EACD,YAAMgC,eAAe,GAAG,KAAKC,mBAAL,CAAyBxE,SAAzB,CAAxB;;EACA,YAAMuE,eAAe,CAACV,eAAhB,CAAgCzJ,OAAO,CAACqF,GAAxC,CAAN;EACA,YAAM8E,eAAe,CAACpD,aAAhB,EAAN;EACH,KAlBD;;EAmBA,IAA2C;EACvC,UAAI,EAAEY,MAAM,CAACS,UAAP,IAAqBT,MAAM,CAACU,aAA9B,CAAJ,EAAkD;EAC9C,cAAM,IAAIC,4BAAJ,CAAiB,6BAAjB,EAAgD;EAClDN,UAAAA,UAAU,EAAE,oBADsC;EAElDC,UAAAA,SAAS,EAAE,QAFuC;EAGlDC,UAAAA,QAAQ,EAAE;EAHwC,SAAhD,CAAN;EAKH;;EACD,UAAIP,MAAM,CAACS,UAAX,EAAuB;EACnBN,QAAAA,gBAAM,CAACC,MAAP,CAAcJ,MAAM,CAACS,UAArB,EAAiC,QAAjC,EAA2C;EACvCJ,UAAAA,UAAU,EAAE,oBAD2B;EAEvCC,UAAAA,SAAS,EAAE,QAF4B;EAGvCC,UAAAA,QAAQ,EAAE,aAH6B;EAIvCC,UAAAA,SAAS,EAAE;EAJ4B,SAA3C;EAMH;;EACD,UAAIR,MAAM,CAACU,aAAX,EAA0B;EACtBP,QAAAA,gBAAM,CAACC,MAAP,CAAcJ,MAAM,CAACU,aAArB,EAAoC,QAApC,EAA8C;EAC1CL,UAAAA,UAAU,EAAE,oBAD8B;EAE1CC,UAAAA,SAAS,EAAE,QAF+B;EAG1CC,UAAAA,QAAQ,EAAE,aAHgC;EAI1CC,UAAAA,SAAS,EAAE;EAJ+B,SAA9C;EAMH;EACJ;;EACD,SAAKyC,OAAL,GAAejD,MAAf;EACA,SAAKa,cAAL,GAAsBb,MAAM,CAACU,aAA7B;EACA,SAAKwC,iBAAL,GAAyB,IAAI5G,GAAJ,EAAzB;;EACA,QAAI0D,MAAM,CAACmD,iBAAX,EAA8B;EAC1BC,MAAAA,wDAA0B,CAAC,MAAM,KAAKC,sBAAL,EAAP,CAA1B;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACIZ,EAAAA,mBAAmB,CAACxE,SAAD,EAAY;EAC3B,QAAIA,SAAS,KAAKqF,wBAAU,CAACC,cAAX,EAAlB,EAA+C;EAC3C,YAAM,IAAI5C,4BAAJ,CAAiB,2BAAjB,CAAN;EACH;;EACD,QAAI6B,eAAe,GAAG,KAAKU,iBAAL,CAAuBtJ,GAAvB,CAA2BqE,SAA3B,CAAtB;;EACA,QAAI,CAACuE,eAAL,EAAsB;EAClBA,MAAAA,eAAe,GAAG,IAAIzC,eAAJ,CAAoB9B,SAApB,EAA+B,KAAKgF,OAApC,CAAlB;;EACA,WAAKC,iBAAL,CAAuB/J,GAAvB,CAA2B8E,SAA3B,EAAsCuE,eAAtC;EACH;;EACD,WAAOA,eAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;;EACID,EAAAA,oBAAoB,CAACF,cAAD,EAAiB;EACjC,QAAI,CAAC,KAAKxB,cAAV,EAA0B;EACtB;EACA,aAAO,IAAP;EACH,KAJgC;EAMjC;EACA;;;EACA,UAAM2C,mBAAmB,GAAG,KAAKC,uBAAL,CAA6BpB,cAA7B,CAA5B;;EACA,QAAImB,mBAAmB,KAAK,IAA5B,EAAkC;EAC9B;EACA,aAAO,IAAP;EACH,KAZgC;EAcjC;;;EACA,UAAMtC,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;EACA,WAAOsC,mBAAmB,IAAItC,GAAG,GAAG,KAAKL,cAAL,GAAsB,IAA1D;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI4C,EAAAA,uBAAuB,CAACpB,cAAD,EAAiB;EACpC,QAAI,CAACA,cAAc,CAACqB,OAAf,CAAuBnK,GAAvB,CAA2B,MAA3B,CAAL,EAAyC;EACrC,aAAO,IAAP;EACH;;EACD,UAAMoK,UAAU,GAAGtB,cAAc,CAACqB,OAAf,CAAuB9J,GAAvB,CAA2B,MAA3B,CAAnB;EACA,UAAMgK,UAAU,GAAG,IAAI3C,IAAJ,CAAS0C,UAAT,CAAnB;EACA,UAAME,UAAU,GAAGD,UAAU,CAACE,OAAX,EAAnB,CANoC;EAQpC;;EACA,QAAIC,KAAK,CAACF,UAAD,CAAT,EAAuB;EACnB,aAAO,IAAP;EACH;;EACD,WAAOA,UAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMR,sBAAN,GAA+B;EAC3B;EACA;EACA,SAAK,MAAM,CAACpF,SAAD,EAAYuE,eAAZ,CAAX,IAA2C,KAAKU,iBAAhD,EAAmE;EAC/D,YAAM/F,IAAI,CAACkE,MAAL,CAAYxB,MAAZ,CAAmB5B,SAAnB,CAAN;EACA,YAAMuE,eAAe,CAAC3C,MAAhB,EAAN;EACH,KAN0B;;;EAQ3B,SAAKqD,iBAAL,GAAyB,IAAI5G,GAAJ,EAAzB;EACH;;EApNkB;;;;;;;;;;;"}