{"version": 3, "file": "workbox-strategies.dev.js", "sources": ["../_version.js", "../StrategyHandler.js", "../Strategy.js", "../utils/messages.js", "../CacheFirst.js", "../CacheOnly.js", "../plugins/cacheOkAndOpaquePlugin.js", "../NetworkFirst.js", "../NetworkOnly.js", "../StaleWhileRevalidate.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:strategies:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheMatchIgnoreParams } from 'workbox-core/_private/cacheMatchIgnoreParams.js';\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { executeQuotaErrorCallbacks } from 'workbox-core/_private/executeQuotaErrorCallbacks.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\nfunction toRequest(input) {\n    return typeof input === 'string' ? new Request(input) : input;\n}\n/**\n * A class created every time a Strategy instance instance calls\n * {@link workbox-strategies.Strategy~handle} or\n * {@link workbox-strategies.Strategy~handleAll} that wraps all fetch and\n * cache actions around plugin callbacks and keeps track of when the strategy\n * is \"done\" (i.e. all added `event.waitUntil()` promises have resolved).\n *\n * @memberof workbox-strategies\n */\nclass StrategyHandler {\n    /**\n     * Creates a new instance associated with the passed strategy and event\n     * that's handling the request.\n     *\n     * The constructor also initializes the state that will be passed to each of\n     * the plugins handling this request.\n     *\n     * @param {workbox-strategies.Strategy} strategy\n     * @param {Object} options\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params] The return value from the\n     *     {@link workbox-routing~matchCallback} (if applicable).\n     */\n    constructor(strategy, options) {\n        this._cacheKeys = {};\n        /**\n         * The request the strategy is performing (passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * @name request\n         * @instance\n         * @type {Request}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * The event associated with this request.\n         * @name event\n         * @instance\n         * @type {ExtendableEvent}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `URL` instance of `request.url` (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `url` param will be present if the strategy was invoked\n         * from a workbox `Route` object.\n         * @name url\n         * @instance\n         * @type {URL|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `param` value (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `param` param will be present if the strategy was invoked\n         * from a workbox `Route` object and the\n         * {@link workbox-routing~matchCallback} returned\n         * a truthy value (it will be that value).\n         * @name params\n         * @instance\n         * @type {*|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(options.event, ExtendableEvent, {\n                moduleName: 'workbox-strategies',\n                className: 'StrategyHandler',\n                funcName: 'constructor',\n                paramName: 'options.event',\n            });\n        }\n        Object.assign(this, options);\n        this.event = options.event;\n        this._strategy = strategy;\n        this._handlerDeferred = new Deferred();\n        this._extendLifetimePromises = [];\n        // Copy the plugins list (since it's mutable on the strategy),\n        // so any mutations don't affect this handler instance.\n        this._plugins = [...strategy.plugins];\n        this._pluginStateMap = new Map();\n        for (const plugin of this._plugins) {\n            this._pluginStateMap.set(plugin, {});\n        }\n        this.event.waitUntil(this._handlerDeferred.promise);\n    }\n    /**\n     * Fetches a given request (and invokes any applicable plugin callback\n     * methods) using the `fetchOptions` (for non-navigation requests) and\n     * `plugins` defined on the `Strategy` object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - `requestWillFetch()`\n     * - `fetchDidSucceed()`\n     * - `fetchDidFail()`\n     *\n     * @param {Request|string} input The URL or request to fetch.\n     * @return {Promise<Response>}\n     */\n    async fetch(input) {\n        const { event } = this;\n        let request = toRequest(input);\n        if (request.mode === 'navigate' &&\n            event instanceof FetchEvent &&\n            event.preloadResponse) {\n            const possiblePreloadResponse = (await event.preloadResponse);\n            if (possiblePreloadResponse) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Using a preloaded navigation response for ` +\n                        `'${getFriendlyURL(request.url)}'`);\n                }\n                return possiblePreloadResponse;\n            }\n        }\n        // If there is a fetchDidFail plugin, we need to save a clone of the\n        // original request before it's either modified by a requestWillFetch\n        // plugin or before the original request's body is consumed via fetch().\n        const originalRequest = this.hasCallback('fetchDidFail')\n            ? request.clone()\n            : null;\n        try {\n            for (const cb of this.iterateCallbacks('requestWillFetch')) {\n                request = await cb({ request: request.clone(), event });\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                throw new WorkboxError('plugin-error-request-will-fetch', {\n                    thrownErrorMessage: err.message,\n                });\n            }\n        }\n        // The request can be altered by plugins with `requestWillFetch` making\n        // the original request (most likely from a `fetch` event) different\n        // from the Request we make. Pass both to `fetchDidFail` to aid debugging.\n        const pluginFilteredRequest = request.clone();\n        try {\n            let fetchResponse;\n            // See https://github.com/GoogleChrome/workbox/issues/1796\n            fetchResponse = await fetch(request, request.mode === 'navigate' ? undefined : this._strategy.fetchOptions);\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' returned a response with ` +\n                    `status '${fetchResponse.status}'.`);\n            }\n            for (const callback of this.iterateCallbacks('fetchDidSucceed')) {\n                fetchResponse = await callback({\n                    event,\n                    request: pluginFilteredRequest,\n                    response: fetchResponse,\n                });\n            }\n            return fetchResponse;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' threw an error.`, error);\n            }\n            // `originalRequest` will only exist if a `fetchDidFail` callback\n            // is being used (see above).\n            if (originalRequest) {\n                await this.runCallbacks('fetchDidFail', {\n                    error: error,\n                    event,\n                    originalRequest: originalRequest.clone(),\n                    request: pluginFilteredRequest.clone(),\n                });\n            }\n            throw error;\n        }\n    }\n    /**\n     * Calls `this.fetch()` and (in the background) runs `this.cachePut()` on\n     * the response generated by `this.fetch()`.\n     *\n     * The call to `this.cachePut()` automatically invokes `this.waitUntil()`,\n     * so you do not have to manually call `waitUntil()` on the event.\n     *\n     * @param {Request|string} input The request or URL to fetch and cache.\n     * @return {Promise<Response>}\n     */\n    async fetchAndCachePut(input) {\n        const response = await this.fetch(input);\n        const responseClone = response.clone();\n        void this.waitUntil(this.cachePut(input, responseClone));\n        return response;\n    }\n    /**\n     * Matches a request from the cache (and invokes any applicable plugin\n     * callback methods) using the `cacheName`, `matchOptions`, and `plugins`\n     * defined on the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillByUsed()\n     * - cachedResponseWillByUsed()\n     *\n     * @param {Request|string} key The Request or URL to use as the cache key.\n     * @return {Promise<Response|undefined>} A matching response, if found.\n     */\n    async cacheMatch(key) {\n        const request = toRequest(key);\n        let cachedResponse;\n        const { cacheName, matchOptions } = this._strategy;\n        const effectiveRequest = await this.getCacheKey(request, 'read');\n        const multiMatchOptions = Object.assign(Object.assign({}, matchOptions), { cacheName });\n        cachedResponse = await caches.match(effectiveRequest, multiMatchOptions);\n        if (process.env.NODE_ENV !== 'production') {\n            if (cachedResponse) {\n                logger.debug(`Found a cached response in '${cacheName}'.`);\n            }\n            else {\n                logger.debug(`No cached response found in '${cacheName}'.`);\n            }\n        }\n        for (const callback of this.iterateCallbacks('cachedResponseWillBeUsed')) {\n            cachedResponse =\n                (await callback({\n                    cacheName,\n                    matchOptions,\n                    cachedResponse,\n                    request: effectiveRequest,\n                    event: this.event,\n                })) || undefined;\n        }\n        return cachedResponse;\n    }\n    /**\n     * Puts a request/response pair in the cache (and invokes any applicable\n     * plugin callback methods) using the `cacheName` and `plugins` defined on\n     * the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillByUsed()\n     * - cacheWillUpdate()\n     * - cacheDidUpdate()\n     *\n     * @param {Request|string} key The request or URL to use as the cache key.\n     * @param {Response} response The response to cache.\n     * @return {Promise<boolean>} `false` if a cacheWillUpdate caused the response\n     * not be cached, and `true` otherwise.\n     */\n    async cachePut(key, response) {\n        const request = toRequest(key);\n        // Run in the next task to avoid blocking other cache reads.\n        // https://github.com/w3c/ServiceWorker/issues/1397\n        await timeout(0);\n        const effectiveRequest = await this.getCacheKey(request, 'write');\n        if (process.env.NODE_ENV !== 'production') {\n            if (effectiveRequest.method && effectiveRequest.method !== 'GET') {\n                throw new WorkboxError('attempt-to-cache-non-get-request', {\n                    url: getFriendlyURL(effectiveRequest.url),\n                    method: effectiveRequest.method,\n                });\n            }\n            // See https://github.com/GoogleChrome/workbox/issues/2818\n            const vary = response.headers.get('Vary');\n            if (vary) {\n                logger.debug(`The response for ${getFriendlyURL(effectiveRequest.url)} ` +\n                    `has a 'Vary: ${vary}' header. ` +\n                    `Consider setting the {ignoreVary: true} option on your strategy ` +\n                    `to ensure cache matching and deletion works as expected.`);\n            }\n        }\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(`Cannot cache non-existent response for ` +\n                    `'${getFriendlyURL(effectiveRequest.url)}'.`);\n            }\n            throw new WorkboxError('cache-put-with-no-response', {\n                url: getFriendlyURL(effectiveRequest.url),\n            });\n        }\n        const responseToCache = await this._ensureResponseSafeToCache(response);\n        if (!responseToCache) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Response '${getFriendlyURL(effectiveRequest.url)}' ` +\n                    `will not be cached.`, responseToCache);\n            }\n            return false;\n        }\n        const { cacheName, matchOptions } = this._strategy;\n        const cache = await self.caches.open(cacheName);\n        const hasCacheUpdateCallback = this.hasCallback('cacheDidUpdate');\n        const oldResponse = hasCacheUpdateCallback\n            ? await cacheMatchIgnoreParams(\n            // TODO(philipwalton): the `__WB_REVISION__` param is a precaching\n            // feature. Consider into ways to only add this behavior if using\n            // precaching.\n            cache, effectiveRequest.clone(), ['__WB_REVISION__'], matchOptions)\n            : null;\n        if (process.env.NODE_ENV !== 'production') {\n            logger.debug(`Updating the '${cacheName}' cache with a new Response ` +\n                `for ${getFriendlyURL(effectiveRequest.url)}.`);\n        }\n        try {\n            await cache.put(effectiveRequest, hasCacheUpdateCallback ? responseToCache.clone() : responseToCache);\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                // See https://developer.mozilla.org/en-US/docs/Web/API/DOMException#exception-QuotaExceededError\n                if (error.name === 'QuotaExceededError') {\n                    await executeQuotaErrorCallbacks();\n                }\n                throw error;\n            }\n        }\n        for (const callback of this.iterateCallbacks('cacheDidUpdate')) {\n            await callback({\n                cacheName,\n                oldResponse,\n                newResponse: responseToCache.clone(),\n                request: effectiveRequest,\n                event: this.event,\n            });\n        }\n        return true;\n    }\n    /**\n     * Checks the list of plugins for the `cacheKeyWillBeUsed` callback, and\n     * executes any of those callbacks found in sequence. The final `Request`\n     * object returned by the last plugin is treated as the cache key for cache\n     * reads and/or writes. If no `cacheKeyWillBeUsed` plugin callbacks have\n     * been registered, the passed request is returned unmodified\n     *\n     * @param {Request} request\n     * @param {string} mode\n     * @return {Promise<Request>}\n     */\n    async getCacheKey(request, mode) {\n        const key = `${request.url} | ${mode}`;\n        if (!this._cacheKeys[key]) {\n            let effectiveRequest = request;\n            for (const callback of this.iterateCallbacks('cacheKeyWillBeUsed')) {\n                effectiveRequest = toRequest(await callback({\n                    mode,\n                    request: effectiveRequest,\n                    event: this.event,\n                    // params has a type any can't change right now.\n                    params: this.params, // eslint-disable-line\n                }));\n            }\n            this._cacheKeys[key] = effectiveRequest;\n        }\n        return this._cacheKeys[key];\n    }\n    /**\n     * Returns true if the strategy has at least one plugin with the given\n     * callback.\n     *\n     * @param {string} name The name of the callback to check for.\n     * @return {boolean}\n     */\n    hasCallback(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (name in plugin) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * Runs all plugin callbacks matching the given name, in order, passing the\n     * given param object (merged ith the current plugin state) as the only\n     * argument.\n     *\n     * Note: since this method runs all plugins, it's not suitable for cases\n     * where the return value of a callback needs to be applied prior to calling\n     * the next callback. See\n     * {@link workbox-strategies.StrategyHandler#iterateCallbacks}\n     * below for how to handle that case.\n     *\n     * @param {string} name The name of the callback to run within each plugin.\n     * @param {Object} param The object to pass as the first (and only) param\n     *     when executing each callback. This object will be merged with the\n     *     current plugin state prior to callback execution.\n     */\n    async runCallbacks(name, param) {\n        for (const callback of this.iterateCallbacks(name)) {\n            // TODO(philipwalton): not sure why `any` is needed. It seems like\n            // this should work with `as WorkboxPluginCallbackParam[C]`.\n            await callback(param);\n        }\n    }\n    /**\n     * Accepts a callback and returns an iterable of matching plugin callbacks,\n     * where each callback is wrapped with the current handler state (i.e. when\n     * you call each callback, whatever object parameter you pass it will\n     * be merged with the plugin's current state).\n     *\n     * @param {string} name The name fo the callback to run\n     * @return {Array<Function>}\n     */\n    *iterateCallbacks(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (typeof plugin[name] === 'function') {\n                const state = this._pluginStateMap.get(plugin);\n                const statefulCallback = (param) => {\n                    const statefulParam = Object.assign(Object.assign({}, param), { state });\n                    // TODO(philipwalton): not sure why `any` is needed. It seems like\n                    // this should work with `as WorkboxPluginCallbackParam[C]`.\n                    return plugin[name](statefulParam);\n                };\n                yield statefulCallback;\n            }\n        }\n    }\n    /**\n     * Adds a promise to the\n     * [extend lifetime promises]{@link https://w3c.github.io/ServiceWorker/#extendableevent-extend-lifetime-promises}\n     * of the event event associated with the request being handled (usually a\n     * `FetchEvent`).\n     *\n     * Note: you can await\n     * {@link workbox-strategies.StrategyHandler~doneWaiting}\n     * to know when all added promises have settled.\n     *\n     * @param {Promise} promise A promise to add to the extend lifetime promises\n     *     of the event that triggered the request.\n     */\n    waitUntil(promise) {\n        this._extendLifetimePromises.push(promise);\n        return promise;\n    }\n    /**\n     * Returns a promise that resolves once all promises passed to\n     * {@link workbox-strategies.StrategyHandler~waitUntil}\n     * have settled.\n     *\n     * Note: any work done after `doneWaiting()` settles should be manually\n     * passed to an event's `waitUntil()` method (not this handler's\n     * `waitUntil()` method), otherwise the service worker thread my be killed\n     * prior to your work completing.\n     */\n    async doneWaiting() {\n        let promise;\n        while ((promise = this._extendLifetimePromises.shift())) {\n            await promise;\n        }\n    }\n    /**\n     * Stops running the strategy and immediately resolves any pending\n     * `waitUntil()` promises.\n     */\n    destroy() {\n        this._handlerDeferred.resolve(null);\n    }\n    /**\n     * This method will call cacheWillUpdate on the available plugins (or use\n     * status === 200) to determine if the Response is safe and valid to cache.\n     *\n     * @param {Request} options.request\n     * @param {Response} options.response\n     * @return {Promise<Response|undefined>}\n     *\n     * @private\n     */\n    async _ensureResponseSafeToCache(response) {\n        let responseToCache = response;\n        let pluginsUsed = false;\n        for (const callback of this.iterateCallbacks('cacheWillUpdate')) {\n            responseToCache =\n                (await callback({\n                    request: this.request,\n                    response: responseToCache,\n                    event: this.event,\n                })) || undefined;\n            pluginsUsed = true;\n            if (!responseToCache) {\n                break;\n            }\n        }\n        if (!pluginsUsed) {\n            if (responseToCache && responseToCache.status !== 200) {\n                responseToCache = undefined;\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (responseToCache) {\n                    if (responseToCache.status !== 200) {\n                        if (responseToCache.status === 0) {\n                            logger.warn(`The response for '${this.request.url}' ` +\n                                `is an opaque response. The caching strategy that you're ` +\n                                `using will not cache opaque responses by default.`);\n                        }\n                        else {\n                            logger.debug(`The response for '${this.request.url}' ` +\n                                `returned a status code of '${response.status}' and won't ` +\n                                `be cached as a result.`);\n                        }\n                    }\n                }\n            }\n        }\n        return responseToCache;\n    }\n}\nexport { StrategyHandler };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { StrategyHandler } from './StrategyHandler.js';\nimport './_version.js';\n/**\n * An abstract base class that all other strategy classes must extend from:\n *\n * @memberof workbox-strategies\n */\nclass Strategy {\n    /**\n     * Creates a new instance of the strategy and sets all documented option\n     * properties as public instance properties.\n     *\n     * Note: if a custom strategy class extends the base Strategy class and does\n     * not need more than these properties, it does not need to define its own\n     * constructor.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     */\n    constructor(options = {}) {\n        /**\n         * Cache name to store and retrieve\n         * requests. Defaults to the cache names provided by\n         * {@link workbox-core.cacheNames}.\n         *\n         * @type {string}\n         */\n        this.cacheName = cacheNames.getRuntimeName(options.cacheName);\n        /**\n         * The list\n         * [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n         * used by this strategy.\n         *\n         * @type {Array<Object>}\n         */\n        this.plugins = options.plugins || [];\n        /**\n         * Values passed along to the\n         * [`init`]{@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters}\n         * of all fetch() requests made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.fetchOptions = options.fetchOptions;\n        /**\n         * The\n         * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n         * for any `cache.match()` or `cache.put()` calls made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.matchOptions = options.matchOptions;\n    }\n    /**\n     * Perform a request strategy and returns a `Promise` that will resolve with\n     * a `Response`, invoking all relevant plugin callbacks.\n     *\n     * When a strategy instance is registered with a Workbox\n     * {@link workbox-routing.Route}, this method is automatically\n     * called when the route matches.\n     *\n     * Alternatively, this method can be used in a standalone `FetchEvent`\n     * listener by passing it to `event.respondWith()`.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     */\n    handle(options) {\n        const [responseDone] = this.handleAll(options);\n        return responseDone;\n    }\n    /**\n     * Similar to {@link workbox-strategies.Strategy~handle}, but\n     * instead of just returning a `Promise` that resolves to a `Response` it\n     * it will return an tuple of `[response, done]` promises, where the former\n     * (`response`) is equivalent to what `handle()` returns, and the latter is a\n     * Promise that will resolve once any promises that were added to\n     * `event.waitUntil()` as part of performing the strategy have completed.\n     *\n     * You can await the `done` promise to ensure any extra work performed by\n     * the strategy (usually caching responses) completes successfully.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     * @return {Array<Promise>} A tuple of [response, done]\n     *     promises that can be used to determine when the response resolves as\n     *     well as when the handler has completed all its work.\n     */\n    handleAll(options) {\n        // Allow for flexible options to be passed.\n        if (options instanceof FetchEvent) {\n            options = {\n                event: options,\n                request: options.request,\n            };\n        }\n        const event = options.event;\n        const request = typeof options.request === 'string'\n            ? new Request(options.request)\n            : options.request;\n        const params = 'params' in options ? options.params : undefined;\n        const handler = new StrategyHandler(this, { event, request, params });\n        const responseDone = this._getResponse(handler, request, event);\n        const handlerDone = this._awaitComplete(responseDone, handler, request, event);\n        // Return an array of promises, suitable for use with Promise.all().\n        return [responseDone, handlerDone];\n    }\n    async _getResponse(handler, request, event) {\n        await handler.runCallbacks('handlerWillStart', { event, request });\n        let response = undefined;\n        try {\n            response = await this._handle(request, handler);\n            // The \"official\" Strategy subclasses all throw this error automatically,\n            // but in case a third-party Strategy doesn't, ensure that we have a\n            // consistent failure when there's no response or an error response.\n            if (!response || response.type === 'error') {\n                throw new WorkboxError('no-response', { url: request.url });\n            }\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                for (const callback of handler.iterateCallbacks('handlerDidError')) {\n                    response = await callback({ error, event, request });\n                    if (response) {\n                        break;\n                    }\n                }\n            }\n            if (!response) {\n                throw error;\n            }\n            else if (process.env.NODE_ENV !== 'production') {\n                logger.log(`While responding to '${getFriendlyURL(request.url)}', ` +\n                    `an ${error instanceof Error ? error.toString() : ''} error occurred. Using a fallback response provided by ` +\n                    `a handlerDidError plugin.`);\n            }\n        }\n        for (const callback of handler.iterateCallbacks('handlerWillRespond')) {\n            response = await callback({ event, request, response });\n        }\n        return response;\n    }\n    async _awaitComplete(responseDone, handler, request, event) {\n        let response;\n        let error;\n        try {\n            response = await responseDone;\n        }\n        catch (error) {\n            // Ignore errors, as response errors should be caught via the `response`\n            // promise above. The `done` promise will only throw for errors in\n            // promises passed to `handler.waitUntil()`.\n        }\n        try {\n            await handler.runCallbacks('handlerDidRespond', {\n                event,\n                request,\n                response,\n            });\n            await handler.doneWaiting();\n        }\n        catch (waitUntilError) {\n            if (waitUntilError instanceof Error) {\n                error = waitUntilError;\n            }\n        }\n        await handler.runCallbacks('handlerDidComplete', {\n            event,\n            request,\n            response,\n            error: error,\n        });\n        handler.destroy();\n        if (error) {\n            throw error;\n        }\n    }\n}\nexport { Strategy };\n/**\n * Classes extending the `Strategy` based class should implement this method,\n * and leverage the {@link workbox-strategies.StrategyHandler}\n * arg to perform all fetching and cache logic, which will ensure all relevant\n * cache, cache options, fetch options and plugins are used (per the current\n * strategy instance).\n *\n * @name _handle\n * @instance\n * @abstract\n * @function\n * @param {Request} request\n * @param {workbox-strategies.StrategyHandler} handler\n * @return {Promise<Response>}\n *\n * @memberof workbox-strategies.Strategy\n */\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport '../_version.js';\nexport const messages = {\n    strategyStart: (strategyName, request) => `Using ${strategyName} to respond to '${getFriendlyURL(request.url)}'`,\n    printFinalResponse: (response) => {\n        if (response) {\n            logger.groupCollapsed(`View the final response here.`);\n            logger.log(response || '[No response returned]');\n            logger.groupEnd();\n        }\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a [cache-first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#cache-first-falling-back-to-network)\n * request strategy.\n *\n * A cache first strategy is useful for assets that have been revisioned,\n * such as URLs like `/styles/example.a8f5f1.css`, since they\n * can be cached for long periods of time.\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass CacheFirst extends Strategy {\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'makeRequest',\n                paramName: 'request',\n            });\n        }\n        let response = await handler.cacheMatch(request);\n        let error = undefined;\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this.cacheName}' cache. ` +\n                    `Will respond with a network request.`);\n            }\n            try {\n                response = await handler.fetchAndCachePut(request);\n            }\n            catch (err) {\n                if (err instanceof Error) {\n                    error = err;\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Got response from network.`);\n                }\n                else {\n                    logs.push(`Unable to get a response from the network.`);\n                }\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this.cacheName}' cache.`);\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { CacheFirst };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a [cache-only](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#cache-only)\n * request strategy.\n *\n * This class is useful if you want to take advantage of any\n * [Workbox plugins](https://developer.chrome.com/docs/workbox/using-plugins/).\n *\n * If there is no cache match, this will throw a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass CacheOnly extends Strategy {\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'makeRequest',\n                paramName: 'request',\n            });\n        }\n        const response = await handler.cacheMatch(request);\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            if (response) {\n                logger.log(`Found a cached response in the '${this.cacheName}' ` + `cache.`);\n                messages.printFinalResponse(response);\n            }\n            else {\n                logger.log(`No response found in the '${this.cacheName}' cache.`);\n            }\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url });\n        }\n        return response;\n    }\n}\nexport { CacheOnly };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const cacheOkAndOpaquePlugin = {\n    /**\n     * Returns a valid response (to allow caching) if the status is 200 (OK) or\n     * 0 (opaque).\n     *\n     * @param {Object} options\n     * @param {Response} options.response\n     * @return {Response|null}\n     *\n     * @private\n     */\n    cacheWillUpdate: async ({ response }) => {\n        if (response.status === 200 || response.status === 0) {\n            return response;\n        }\n        return null;\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [network first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#network-first-falling-back-to-cache)\n * request strategy.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n * Opaque responses are are cross-origin requests where the response doesn't\n * support [CORS](https://enable-cors.org/).\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass NetworkFirst extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     * @param {number} [options.networkTimeoutSeconds] If set, any network requests\n     * that fail to respond within the timeout will fallback to the cache.\n     *\n     * This option can be used to combat\n     * \"[lie-fi]{@link https://developers.google.com/web/fundamentals/performance/poor-connectivity/#lie-fi}\"\n     * scenarios.\n     */\n    constructor(options = {}) {\n        super(options);\n        // If this instance contains no plugins with a 'cacheWillUpdate' callback,\n        // prepend the `cacheOkAndOpaquePlugin` plugin to the plugins list.\n        if (!this.plugins.some((p) => 'cacheWillUpdate' in p)) {\n            this.plugins.unshift(cacheOkAndOpaquePlugin);\n        }\n        this._networkTimeoutSeconds = options.networkTimeoutSeconds || 0;\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._networkTimeoutSeconds) {\n                assert.isType(this._networkTimeoutSeconds, 'number', {\n                    moduleName: 'workbox-strategies',\n                    className: this.constructor.name,\n                    funcName: 'constructor',\n                    paramName: 'networkTimeoutSeconds',\n                });\n            }\n        }\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'handle',\n                paramName: 'makeRequest',\n            });\n        }\n        const promises = [];\n        let timeoutId;\n        if (this._networkTimeoutSeconds) {\n            const { id, promise } = this._getTimeoutPromise({ request, logs, handler });\n            timeoutId = id;\n            promises.push(promise);\n        }\n        const networkPromise = this._getNetworkPromise({\n            timeoutId,\n            request,\n            logs,\n            handler,\n        });\n        promises.push(networkPromise);\n        const response = await handler.waitUntil((async () => {\n            // Promise.race() will resolve as soon as the first promise resolves.\n            return ((await handler.waitUntil(Promise.race(promises))) ||\n                // If Promise.race() resolved with null, it might be due to a network\n                // timeout + a cache miss. If that were to happen, we'd rather wait until\n                // the networkPromise resolves instead of returning null.\n                // Note that it's fine to await an already-resolved promise, so we don't\n                // have to check to see if it's still \"in flight\".\n                (await networkPromise));\n        })());\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url });\n        }\n        return response;\n    }\n    /**\n     * @param {Object} options\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs array\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    _getTimeoutPromise({ request, logs, handler, }) {\n        let timeoutId;\n        const timeoutPromise = new Promise((resolve) => {\n            const onNetworkTimeout = async () => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logs.push(`Timing out the network response at ` +\n                        `${this._networkTimeoutSeconds} seconds.`);\n                }\n                resolve(await handler.cacheMatch(request));\n            };\n            timeoutId = setTimeout(onNetworkTimeout, this._networkTimeoutSeconds * 1000);\n        });\n        return {\n            promise: timeoutPromise,\n            id: timeoutId,\n        };\n    }\n    /**\n     * @param {Object} options\n     * @param {number|undefined} options.timeoutId\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs Array.\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    async _getNetworkPromise({ timeoutId, request, logs, handler, }) {\n        let error;\n        let response;\n        try {\n            response = await handler.fetchAndCachePut(request);\n        }\n        catch (fetchError) {\n            if (fetchError instanceof Error) {\n                error = fetchError;\n            }\n        }\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (response) {\n                logs.push(`Got response from network.`);\n            }\n            else {\n                logs.push(`Unable to get a response from the network. Will respond ` +\n                    `with a cached response.`);\n            }\n        }\n        if (error || !response) {\n            response = await handler.cacheMatch(request);\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Found a cached response in the '${this.cacheName}'` + ` cache.`);\n                }\n                else {\n                    logs.push(`No response found in the '${this.cacheName}' cache.`);\n                }\n            }\n        }\n        return response;\n    }\n}\nexport { NetworkFirst };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [network-only](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#network-only)\n * request strategy.\n *\n * This class is useful if you want to take advantage of any\n * [Workbox plugins](https://developer.chrome.com/docs/workbox/using-plugins/).\n *\n * If the network request fails, this will throw a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass NetworkOnly extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {number} [options.networkTimeoutSeconds] If set, any network requests\n     * that fail to respond within the timeout will result in a network error.\n     */\n    constructor(options = {}) {\n        super(options);\n        this._networkTimeoutSeconds = options.networkTimeoutSeconds || 0;\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: '_handle',\n                paramName: 'request',\n            });\n        }\n        let error = undefined;\n        let response;\n        try {\n            const promises = [\n                handler.fetch(request),\n            ];\n            if (this._networkTimeoutSeconds) {\n                const timeoutPromise = timeout(this._networkTimeoutSeconds * 1000);\n                promises.push(timeoutPromise);\n            }\n            response = await Promise.race(promises);\n            if (!response) {\n                throw new Error(`Timed out the network response after ` +\n                    `${this._networkTimeoutSeconds} seconds.`);\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                error = err;\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            if (response) {\n                logger.log(`Got response from network.`);\n            }\n            else {\n                logger.log(`Unable to get a response from the network.`);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { NetworkOnly };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [stale-while-revalidate](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#stale-while-revalidate)\n * request strategy.\n *\n * Resources are requested from both the cache and the network in parallel.\n * The strategy will respond with the cached version if available, otherwise\n * wait for the network response. The cache is updated with the network response\n * with each successful request.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n * Opaque responses are cross-origin requests where the response doesn't\n * support [CORS](https://enable-cors.org/).\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass StaleWhileRevalidate extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     */\n    constructor(options = {}) {\n        super(options);\n        // If this instance contains no plugins with a 'cacheWillUpdate' callback,\n        // prepend the `cacheOkAndOpaquePlugin` plugin to the plugins list.\n        if (!this.plugins.some((p) => 'cacheWillUpdate' in p)) {\n            this.plugins.unshift(cacheOkAndOpaquePlugin);\n        }\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'handle',\n                paramName: 'request',\n            });\n        }\n        const fetchAndCachePromise = handler.fetchAndCachePut(request).catch(() => {\n            // Swallow this error because a 'no-response' error will be thrown in\n            // main handler return flow. This will be in the `waitUntil()` flow.\n        });\n        void handler.waitUntil(fetchAndCachePromise);\n        let response = await handler.cacheMatch(request);\n        let error;\n        if (response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this.cacheName}'` +\n                    ` cache. Will update with the network response in the background.`);\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this.cacheName}' cache. ` +\n                    `Will wait for the network response.`);\n            }\n            try {\n                // NOTE(philipwalton): Really annoying that we have to type cast here.\n                // https://github.com/microsoft/TypeScript/issues/20006\n                response = (await fetchAndCachePromise);\n            }\n            catch (err) {\n                if (err instanceof Error) {\n                    error = err;\n                }\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { StaleWhileRevalidate };\n"], "names": ["self", "_", "e", "toRequest", "input", "Request", "StrategyHandler", "constructor", "strategy", "options", "_cacheKeys", "assert", "isInstance", "event", "ExtendableEvent", "moduleName", "className", "funcName", "paramName", "Object", "assign", "_strategy", "_handler<PERSON><PERSON><PERSON><PERSON>", "Deferred", "_extendLifetimePromises", "_plugins", "plugins", "_pluginStateMap", "Map", "plugin", "set", "waitUntil", "promise", "fetch", "request", "mode", "FetchEvent", "preloadResponse", "possiblePreloadResponse", "logger", "log", "getFriendlyURL", "url", "originalRequest", "<PERSON><PERSON><PERSON><PERSON>", "clone", "cb", "iterateCallbacks", "err", "Error", "WorkboxError", "thrownErrorMessage", "message", "pluginFilteredRequest", "fetchResponse", "undefined", "fetchOptions", "process", "debug", "status", "callback", "response", "error", "runCallbacks", "fetchAndCachePut", "responseClone", "cachePut", "cacheMatch", "key", "cachedResponse", "cacheName", "matchOptions", "effectiveRequest", "get<PERSON><PERSON><PERSON><PERSON>", "multiMatchOptions", "caches", "match", "timeout", "method", "vary", "headers", "get", "responseToCache", "_ensureResponseSafeToCache", "cache", "open", "hasCacheUpdateCallback", "oldResponse", "cacheMatchIgnoreParams", "put", "name", "executeQuotaErrorCallbacks", "newResponse", "params", "param", "state", "stateful<PERSON><PERSON><PERSON>", "statefulParam", "push", "doneWaiting", "shift", "destroy", "resolve", "pluginsUsed", "warn", "Strategy", "cacheNames", "getRuntimeName", "handle", "responseDone", "handleAll", "handler", "_getResponse", "handlerDone", "_awaitComplete", "_handle", "type", "toString", "waitUntilError", "messages", "strategyStart", "strategyName", "printFinalResponse", "groupCollapsed", "groupEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheOkAndOpaquePlugin", "cacheWillUpdate", "NetworkFirst", "some", "p", "unshift", "_networkTimeoutSeconds", "networkTimeoutSeconds", "isType", "promises", "timeoutId", "id", "_getTimeoutPromise", "networkPromise", "_getNetworkPromise", "Promise", "race", "timeoutPromise", "onNetworkTimeout", "setTimeout", "fetchError", "clearTimeout", "NetworkOnly", "StaleWhileRevalidate", "fetchAndCachePromise", "catch"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,0BAAD,CAAJ,IAAoCC,CAAC,EAArC;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;;IAUA,SAASC,SAAT,CAAmBC,KAAnB,EAA0B;IACtB,SAAO,OAAOA,KAAP,KAAiB,QAAjB,GAA4B,IAAIC,OAAJ,CAAYD,KAAZ,CAA5B,GAAiDA,KAAxD;IACH;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,MAAME,eAAN,CAAsB;IAClB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,WAAW,CAACC,QAAD,EAAWC,OAAX,EAAoB;IAC3B,SAAKC,UAAL,GAAkB,EAAlB;IACA;IACR;IACA;IACA;IACA;IACA;IACA;IACA;;IACQ;IACR;IACA;IACA;IACA;IACA;IACA;;IACQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACQ,IAA2C;IACvCC,MAAAA,gBAAM,CAACC,UAAP,CAAkBH,OAAO,CAACI,KAA1B,EAAiCC,eAAjC,EAAkD;IAC9CC,QAAAA,UAAU,EAAE,oBADkC;IAE9CC,QAAAA,SAAS,EAAE,iBAFmC;IAG9CC,QAAAA,QAAQ,EAAE,aAHoC;IAI9CC,QAAAA,SAAS,EAAE;IAJmC,OAAlD;IAMH;;IACDC,IAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBX,OAApB;IACA,SAAKI,KAAL,GAAaJ,OAAO,CAACI,KAArB;IACA,SAAKQ,SAAL,GAAiBb,QAAjB;IACA,SAAKc,gBAAL,GAAwB,IAAIC,oBAAJ,EAAxB;IACA,SAAKC,uBAAL,GAA+B,EAA/B,CAnD2B;IAqD3B;;IACA,SAAKC,QAAL,GAAgB,CAAC,GAAGjB,QAAQ,CAACkB,OAAb,CAAhB;IACA,SAAKC,eAAL,GAAuB,IAAIC,GAAJ,EAAvB;;IACA,SAAK,MAAMC,MAAX,IAAqB,KAAKJ,QAA1B,EAAoC;IAChC,WAAKE,eAAL,CAAqBG,GAArB,CAAyBD,MAAzB,EAAiC,EAAjC;IACH;;IACD,SAAKhB,KAAL,CAAWkB,SAAX,CAAqB,KAAKT,gBAAL,CAAsBU,OAA3C;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMC,KAAN,CAAY7B,KAAZ,EAAmB;IACf,UAAM;IAAES,MAAAA;IAAF,QAAY,IAAlB;IACA,QAAIqB,OAAO,GAAG/B,SAAS,CAACC,KAAD,CAAvB;;IACA,QAAI8B,OAAO,CAACC,IAAR,KAAiB,UAAjB,IACAtB,KAAK,YAAYuB,UADjB,IAEAvB,KAAK,CAACwB,eAFV,EAE2B;IACvB,YAAMC,uBAAuB,GAAI,MAAMzB,KAAK,CAACwB,eAA7C;;IACA,UAAIC,uBAAJ,EAA6B;IACzB,QAA2C;IACvCC,UAAAA,gBAAM,CAACC,GAAP,CAAY,4CAAD,GACN,IAAGC,gCAAc,CAACP,OAAO,CAACQ,GAAT,CAAc,GADpC;IAEH;;IACD,eAAOJ,uBAAP;IACH;IACJ,KAdc;IAgBf;IACA;;;IACA,UAAMK,eAAe,GAAG,KAAKC,WAAL,CAAiB,cAAjB,IAClBV,OAAO,CAACW,KAAR,EADkB,GAElB,IAFN;;IAGA,QAAI;IACA,WAAK,MAAMC,EAAX,IAAiB,KAAKC,gBAAL,CAAsB,kBAAtB,CAAjB,EAA4D;IACxDb,QAAAA,OAAO,GAAG,MAAMY,EAAE,CAAC;IAAEZ,UAAAA,OAAO,EAAEA,OAAO,CAACW,KAAR,EAAX;IAA4BhC,UAAAA;IAA5B,SAAD,CAAlB;IACH;IACJ,KAJD,CAKA,OAAOmC,GAAP,EAAY;IACR,UAAIA,GAAG,YAAYC,KAAnB,EAA0B;IACtB,cAAM,IAAIC,4BAAJ,CAAiB,iCAAjB,EAAoD;IACtDC,UAAAA,kBAAkB,EAAEH,GAAG,CAACI;IAD8B,SAApD,CAAN;IAGH;IACJ,KAhCc;IAkCf;IACA;;;IACA,UAAMC,qBAAqB,GAAGnB,OAAO,CAACW,KAAR,EAA9B;;IACA,QAAI;IACA,UAAIS,aAAJ,CADA;;IAGAA,MAAAA,aAAa,GAAG,MAAMrB,KAAK,CAACC,OAAD,EAAUA,OAAO,CAACC,IAAR,KAAiB,UAAjB,GAA8BoB,SAA9B,GAA0C,KAAKlC,SAAL,CAAemC,YAAnE,CAA3B;;IACA,UAAIC,KAAA,KAAyB,YAA7B,EAA2C;IACvClB,QAAAA,gBAAM,CAACmB,KAAP,CAAc,sBAAD,GACR,IAAGjB,gCAAc,CAACP,OAAO,CAACQ,GAAT,CAAc,6BADvB,GAER,WAAUY,aAAa,CAACK,MAAO,IAFpC;IAGH;;IACD,WAAK,MAAMC,QAAX,IAAuB,KAAKb,gBAAL,CAAsB,iBAAtB,CAAvB,EAAiE;IAC7DO,QAAAA,aAAa,GAAG,MAAMM,QAAQ,CAAC;IAC3B/C,UAAAA,KAD2B;IAE3BqB,UAAAA,OAAO,EAAEmB,qBAFkB;IAG3BQ,UAAAA,QAAQ,EAAEP;IAHiB,SAAD,CAA9B;IAKH;;IACD,aAAOA,aAAP;IACH,KAjBD,CAkBA,OAAOQ,KAAP,EAAc;IACV,MAA2C;IACvCvB,QAAAA,gBAAM,CAACC,GAAP,CAAY,sBAAD,GACN,IAAGC,gCAAc,CAACP,OAAO,CAACQ,GAAT,CAAc,mBADpC,EACwDoB,KADxD;IAEH,OAJS;IAMV;;;IACA,UAAInB,eAAJ,EAAqB;IACjB,cAAM,KAAKoB,YAAL,CAAkB,cAAlB,EAAkC;IACpCD,UAAAA,KAAK,EAAEA,KAD6B;IAEpCjD,UAAAA,KAFoC;IAGpC8B,UAAAA,eAAe,EAAEA,eAAe,CAACE,KAAhB,EAHmB;IAIpCX,UAAAA,OAAO,EAAEmB,qBAAqB,CAACR,KAAtB;IAJ2B,SAAlC,CAAN;IAMH;;IACD,YAAMiB,KAAN;IACH;IACJ;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAME,gBAAN,CAAuB5D,KAAvB,EAA8B;IAC1B,UAAMyD,QAAQ,GAAG,MAAM,KAAK5B,KAAL,CAAW7B,KAAX,CAAvB;IACA,UAAM6D,aAAa,GAAGJ,QAAQ,CAAChB,KAAT,EAAtB;IACA,SAAK,KAAKd,SAAL,CAAe,KAAKmC,QAAL,CAAc9D,KAAd,EAAqB6D,aAArB,CAAf,CAAL;IACA,WAAOJ,QAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMM,UAAN,CAAiBC,GAAjB,EAAsB;IAClB,UAAMlC,OAAO,GAAG/B,SAAS,CAACiE,GAAD,CAAzB;IACA,QAAIC,cAAJ;IACA,UAAM;IAAEC,MAAAA,SAAF;IAAaC,MAAAA;IAAb,QAA8B,KAAKlD,SAAzC;IACA,UAAMmD,gBAAgB,GAAG,MAAM,KAAKC,WAAL,CAAiBvC,OAAjB,EAA0B,MAA1B,CAA/B;IACA,UAAMwC,iBAAiB,GAAGvD,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBmD,YAAlB,CAAd,EAA+C;IAAED,MAAAA;IAAF,KAA/C,CAA1B;IACAD,IAAAA,cAAc,GAAG,MAAMM,MAAM,CAACC,KAAP,CAAaJ,gBAAb,EAA+BE,iBAA/B,CAAvB;;IACA,IAA2C;IACvC,UAAIL,cAAJ,EAAoB;IAChB9B,QAAAA,gBAAM,CAACmB,KAAP,CAAc,+BAA8BY,SAAU,IAAtD;IACH,OAFD,MAGK;IACD/B,QAAAA,gBAAM,CAACmB,KAAP,CAAc,gCAA+BY,SAAU,IAAvD;IACH;IACJ;;IACD,SAAK,MAAMV,QAAX,IAAuB,KAAKb,gBAAL,CAAsB,0BAAtB,CAAvB,EAA0E;IACtEsB,MAAAA,cAAc,GACV,CAAC,MAAMT,QAAQ,CAAC;IACZU,QAAAA,SADY;IAEZC,QAAAA,YAFY;IAGZF,QAAAA,cAHY;IAIZnC,QAAAA,OAAO,EAAEsC,gBAJG;IAKZ3D,QAAAA,KAAK,EAAE,KAAKA;IALA,OAAD,CAAf,KAMO0C,SAPX;IAQH;;IACD,WAAOc,cAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMH,QAAN,CAAeE,GAAf,EAAoBP,QAApB,EAA8B;IAC1B,UAAM3B,OAAO,GAAG/B,SAAS,CAACiE,GAAD,CAAzB,CAD0B;IAG1B;;IACA,UAAMS,kBAAO,CAAC,CAAD,CAAb;IACA,UAAML,gBAAgB,GAAG,MAAM,KAAKC,WAAL,CAAiBvC,OAAjB,EAA0B,OAA1B,CAA/B;;IACA,IAA2C;IACvC,UAAIsC,gBAAgB,CAACM,MAAjB,IAA2BN,gBAAgB,CAACM,MAAjB,KAA4B,KAA3D,EAAkE;IAC9D,cAAM,IAAI5B,4BAAJ,CAAiB,kCAAjB,EAAqD;IACvDR,UAAAA,GAAG,EAAED,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAlB,CADoC;IAEvDoC,UAAAA,MAAM,EAAEN,gBAAgB,CAACM;IAF8B,SAArD,CAAN;IAIH,OANsC;;;IAQvC,YAAMC,IAAI,GAAGlB,QAAQ,CAACmB,OAAT,CAAiBC,GAAjB,CAAqB,MAArB,CAAb;;IACA,UAAIF,IAAJ,EAAU;IACNxC,QAAAA,gBAAM,CAACmB,KAAP,CAAc,oBAAmBjB,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAlB,CAAuB,GAAzD,GACR,gBAAeqC,IAAK,YADZ,GAER,kEAFQ,GAGR,0DAHL;IAIH;IACJ;;IACD,QAAI,CAAClB,QAAL,EAAe;IACX,MAA2C;IACvCtB,QAAAA,gBAAM,CAACuB,KAAP,CAAc,yCAAD,GACR,IAAGrB,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAlB,CAAuB,IAD7C;IAEH;;IACD,YAAM,IAAIQ,4BAAJ,CAAiB,4BAAjB,EAA+C;IACjDR,QAAAA,GAAG,EAAED,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAlB;IAD8B,OAA/C,CAAN;IAGH;;IACD,UAAMwC,eAAe,GAAG,MAAM,KAAKC,0BAAL,CAAgCtB,QAAhC,CAA9B;;IACA,QAAI,CAACqB,eAAL,EAAsB;IAClB,MAA2C;IACvC3C,QAAAA,gBAAM,CAACmB,KAAP,CAAc,aAAYjB,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAlB,CAAuB,IAAlD,GACR,qBADL,EAC2BwC,eAD3B;IAEH;;IACD,aAAO,KAAP;IACH;;IACD,UAAM;IAAEZ,MAAAA,SAAF;IAAaC,MAAAA;IAAb,QAA8B,KAAKlD,SAAzC;IACA,UAAM+D,KAAK,GAAG,MAAMpF,IAAI,CAAC2E,MAAL,CAAYU,IAAZ,CAAiBf,SAAjB,CAApB;IACA,UAAMgB,sBAAsB,GAAG,KAAK1C,WAAL,CAAiB,gBAAjB,CAA/B;IACA,UAAM2C,WAAW,GAAGD,sBAAsB,GACpC,MAAME,gDAAsB;IAE9B;IACA;IACAJ,IAAAA,KAJ8B,EAIvBZ,gBAAgB,CAAC3B,KAAjB,EAJuB,EAIG,CAAC,iBAAD,CAJH,EAIwB0B,YAJxB,CADQ,GAMpC,IANN;;IAOA,IAA2C;IACvChC,MAAAA,gBAAM,CAACmB,KAAP,CAAc,iBAAgBY,SAAU,8BAA3B,GACR,OAAM7B,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAlB,CAAuB,GADhD;IAEH;;IACD,QAAI;IACA,YAAM0C,KAAK,CAACK,GAAN,CAAUjB,gBAAV,EAA4Bc,sBAAsB,GAAGJ,eAAe,CAACrC,KAAhB,EAAH,GAA6BqC,eAA/E,CAAN;IACH,KAFD,CAGA,OAAOpB,KAAP,EAAc;IACV,UAAIA,KAAK,YAAYb,KAArB,EAA4B;IACxB;IACA,YAAIa,KAAK,CAAC4B,IAAN,KAAe,oBAAnB,EAAyC;IACrC,gBAAMC,wDAA0B,EAAhC;IACH;;IACD,cAAM7B,KAAN;IACH;IACJ;;IACD,SAAK,MAAMF,QAAX,IAAuB,KAAKb,gBAAL,CAAsB,gBAAtB,CAAvB,EAAgE;IAC5D,YAAMa,QAAQ,CAAC;IACXU,QAAAA,SADW;IAEXiB,QAAAA,WAFW;IAGXK,QAAAA,WAAW,EAAEV,eAAe,CAACrC,KAAhB,EAHF;IAIXX,QAAAA,OAAO,EAAEsC,gBAJE;IAKX3D,QAAAA,KAAK,EAAE,KAAKA;IALD,OAAD,CAAd;IAOH;;IACD,WAAO,IAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAM4D,WAAN,CAAkBvC,OAAlB,EAA2BC,IAA3B,EAAiC;IAC7B,UAAMiC,GAAG,GAAI,GAAElC,OAAO,CAACQ,GAAI,MAAKP,IAAK,EAArC;;IACA,QAAI,CAAC,KAAKzB,UAAL,CAAgB0D,GAAhB,CAAL,EAA2B;IACvB,UAAII,gBAAgB,GAAGtC,OAAvB;;IACA,WAAK,MAAM0B,QAAX,IAAuB,KAAKb,gBAAL,CAAsB,oBAAtB,CAAvB,EAAoE;IAChEyB,QAAAA,gBAAgB,GAAGrE,SAAS,CAAC,MAAMyD,QAAQ,CAAC;IACxCzB,UAAAA,IADwC;IAExCD,UAAAA,OAAO,EAAEsC,gBAF+B;IAGxC3D,UAAAA,KAAK,EAAE,KAAKA,KAH4B;IAIxC;IACAgF,UAAAA,MAAM,EAAE,KAAKA,MAL2B;;IAAA,SAAD,CAAf,CAA5B;IAOH;;IACD,WAAKnF,UAAL,CAAgB0D,GAAhB,IAAuBI,gBAAvB;IACH;;IACD,WAAO,KAAK9D,UAAL,CAAgB0D,GAAhB,CAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;;;IACIxB,EAAAA,WAAW,CAAC8C,IAAD,EAAO;IACd,SAAK,MAAM7D,MAAX,IAAqB,KAAKR,SAAL,CAAeK,OAApC,EAA6C;IACzC,UAAIgE,IAAI,IAAI7D,MAAZ,EAAoB;IAChB,eAAO,IAAP;IACH;IACJ;;IACD,WAAO,KAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMkC,YAAN,CAAmB2B,IAAnB,EAAyBI,KAAzB,EAAgC;IAC5B,SAAK,MAAMlC,QAAX,IAAuB,KAAKb,gBAAL,CAAsB2C,IAAtB,CAAvB,EAAoD;IAChD;IACA;IACA,YAAM9B,QAAQ,CAACkC,KAAD,CAAd;IACH;IACJ;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,GAAC/C,gBAAD,CAAkB2C,IAAlB,EAAwB;IACpB,SAAK,MAAM7D,MAAX,IAAqB,KAAKR,SAAL,CAAeK,OAApC,EAA6C;IACzC,UAAI,OAAOG,MAAM,CAAC6D,IAAD,CAAb,KAAwB,UAA5B,EAAwC;IACpC,cAAMK,KAAK,GAAG,KAAKpE,eAAL,CAAqBsD,GAArB,CAAyBpD,MAAzB,CAAd;;IACA,cAAMmE,gBAAgB,GAAIF,KAAD,IAAW;IAChC,gBAAMG,aAAa,GAAG9E,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB0E,KAAlB,CAAd,EAAwC;IAAEC,YAAAA;IAAF,WAAxC,CAAtB,CADgC;IAGhC;;IACA,iBAAOlE,MAAM,CAAC6D,IAAD,CAAN,CAAaO,aAAb,CAAP;IACH,SALD;;IAMA,cAAMD,gBAAN;IACH;IACJ;IACJ;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIjE,EAAAA,SAAS,CAACC,OAAD,EAAU;IACf,SAAKR,uBAAL,CAA6B0E,IAA7B,CAAkClE,OAAlC;;IACA,WAAOA,OAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMmE,WAAN,GAAoB;IAChB,QAAInE,OAAJ;;IACA,WAAQA,OAAO,GAAG,KAAKR,uBAAL,CAA6B4E,KAA7B,EAAlB,EAAyD;IACrD,YAAMpE,OAAN;IACH;IACJ;IACD;IACJ;IACA;IACA;;;IACIqE,EAAAA,OAAO,GAAG;IACN,SAAK/E,gBAAL,CAAsBgF,OAAtB,CAA8B,IAA9B;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMnB,0BAAN,CAAiCtB,QAAjC,EAA2C;IACvC,QAAIqB,eAAe,GAAGrB,QAAtB;IACA,QAAI0C,WAAW,GAAG,KAAlB;;IACA,SAAK,MAAM3C,QAAX,IAAuB,KAAKb,gBAAL,CAAsB,iBAAtB,CAAvB,EAAiE;IAC7DmC,MAAAA,eAAe,GACX,CAAC,MAAMtB,QAAQ,CAAC;IACZ1B,QAAAA,OAAO,EAAE,KAAKA,OADF;IAEZ2B,QAAAA,QAAQ,EAAEqB,eAFE;IAGZrE,QAAAA,KAAK,EAAE,KAAKA;IAHA,OAAD,CAAf,KAIO0C,SALX;IAMAgD,MAAAA,WAAW,GAAG,IAAd;;IACA,UAAI,CAACrB,eAAL,EAAsB;IAClB;IACH;IACJ;;IACD,QAAI,CAACqB,WAAL,EAAkB;IACd,UAAIrB,eAAe,IAAIA,eAAe,CAACvB,MAAhB,KAA2B,GAAlD,EAAuD;IACnDuB,QAAAA,eAAe,GAAG3B,SAAlB;IACH;;IACD,MAA2C;IACvC,YAAI2B,eAAJ,EAAqB;IACjB,cAAIA,eAAe,CAACvB,MAAhB,KAA2B,GAA/B,EAAoC;IAChC,gBAAIuB,eAAe,CAACvB,MAAhB,KAA2B,CAA/B,EAAkC;IAC9BpB,cAAAA,gBAAM,CAACiE,IAAP,CAAa,qBAAoB,KAAKtE,OAAL,CAAaQ,GAAI,IAAtC,GACP,0DADO,GAEP,mDAFL;IAGH,aAJD,MAKK;IACDH,cAAAA,gBAAM,CAACmB,KAAP,CAAc,qBAAoB,KAAKxB,OAAL,CAAaQ,GAAI,IAAtC,GACR,8BAA6BmB,QAAQ,CAACF,MAAO,cADrC,GAER,wBAFL;IAGH;IACJ;IACJ;IACJ;IACJ;;IACD,WAAOuB,eAAP;IACH;;IAteiB;;IC5BtB;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;;IACA,MAAMuB,QAAN,CAAe;IACX;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIlG,EAAAA,WAAW,CAACE,OAAO,GAAG,EAAX,EAAe;IACtB;IACR;IACA;IACA;IACA;IACA;IACA;IACQ,SAAK6D,SAAL,GAAiBoC,wBAAU,CAACC,cAAX,CAA0BlG,OAAO,CAAC6D,SAAlC,CAAjB;IACA;IACR;IACA;IACA;IACA;IACA;IACA;;IACQ,SAAK5C,OAAL,GAAejB,OAAO,CAACiB,OAAR,IAAmB,EAAlC;IACA;IACR;IACA;IACA;IACA;IACA;IACA;;IACQ,SAAK8B,YAAL,GAAoB/C,OAAO,CAAC+C,YAA5B;IACA;IACR;IACA;IACA;IACA;IACA;IACA;;IACQ,SAAKe,YAAL,GAAoB9D,OAAO,CAAC8D,YAA5B;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIqC,EAAAA,MAAM,CAACnG,OAAD,EAAU;IACZ,UAAM,CAACoG,YAAD,IAAiB,KAAKC,SAAL,CAAerG,OAAf,CAAvB;IACA,WAAOoG,YAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIC,EAAAA,SAAS,CAACrG,OAAD,EAAU;IACf;IACA,QAAIA,OAAO,YAAY2B,UAAvB,EAAmC;IAC/B3B,MAAAA,OAAO,GAAG;IACNI,QAAAA,KAAK,EAAEJ,OADD;IAENyB,QAAAA,OAAO,EAAEzB,OAAO,CAACyB;IAFX,OAAV;IAIH;;IACD,UAAMrB,KAAK,GAAGJ,OAAO,CAACI,KAAtB;IACA,UAAMqB,OAAO,GAAG,OAAOzB,OAAO,CAACyB,OAAf,KAA2B,QAA3B,GACV,IAAI7B,OAAJ,CAAYI,OAAO,CAACyB,OAApB,CADU,GAEVzB,OAAO,CAACyB,OAFd;IAGA,UAAM2D,MAAM,GAAG,YAAYpF,OAAZ,GAAsBA,OAAO,CAACoF,MAA9B,GAAuCtC,SAAtD;IACA,UAAMwD,OAAO,GAAG,IAAIzG,eAAJ,CAAoB,IAApB,EAA0B;IAAEO,MAAAA,KAAF;IAASqB,MAAAA,OAAT;IAAkB2D,MAAAA;IAAlB,KAA1B,CAAhB;;IACA,UAAMgB,YAAY,GAAG,KAAKG,YAAL,CAAkBD,OAAlB,EAA2B7E,OAA3B,EAAoCrB,KAApC,CAArB;;IACA,UAAMoG,WAAW,GAAG,KAAKC,cAAL,CAAoBL,YAApB,EAAkCE,OAAlC,EAA2C7E,OAA3C,EAAoDrB,KAApD,CAApB,CAfe;;;IAiBf,WAAO,CAACgG,YAAD,EAAeI,WAAf,CAAP;IACH;;IACD,QAAMD,YAAN,CAAmBD,OAAnB,EAA4B7E,OAA5B,EAAqCrB,KAArC,EAA4C;IACxC,UAAMkG,OAAO,CAAChD,YAAR,CAAqB,kBAArB,EAAyC;IAAElD,MAAAA,KAAF;IAASqB,MAAAA;IAAT,KAAzC,CAAN;IACA,QAAI2B,QAAQ,GAAGN,SAAf;;IACA,QAAI;IACAM,MAAAA,QAAQ,GAAG,MAAM,KAAKsD,OAAL,CAAajF,OAAb,EAAsB6E,OAAtB,CAAjB,CADA;IAGA;IACA;;IACA,UAAI,CAAClD,QAAD,IAAaA,QAAQ,CAACuD,IAAT,KAAkB,OAAnC,EAA4C;IACxC,cAAM,IAAIlE,4BAAJ,CAAiB,aAAjB,EAAgC;IAAER,UAAAA,GAAG,EAAER,OAAO,CAACQ;IAAf,SAAhC,CAAN;IACH;IACJ,KARD,CASA,OAAOoB,KAAP,EAAc;IACV,UAAIA,KAAK,YAAYb,KAArB,EAA4B;IACxB,aAAK,MAAMW,QAAX,IAAuBmD,OAAO,CAAChE,gBAAR,CAAyB,iBAAzB,CAAvB,EAAoE;IAChEc,UAAAA,QAAQ,GAAG,MAAMD,QAAQ,CAAC;IAAEE,YAAAA,KAAF;IAASjD,YAAAA,KAAT;IAAgBqB,YAAAA;IAAhB,WAAD,CAAzB;;IACA,cAAI2B,QAAJ,EAAc;IACV;IACH;IACJ;IACJ;;IACD,UAAI,CAACA,QAAL,EAAe;IACX,cAAMC,KAAN;IACH,OAFD,MAGgD;IAC5CvB,QAAAA,gBAAM,CAACC,GAAP,CAAY,wBAAuBC,gCAAc,CAACP,OAAO,CAACQ,GAAT,CAAc,KAApD,GACN,MAAKoB,KAAK,YAAYb,KAAjB,GAAyBa,KAAK,CAACuD,QAAN,EAAzB,GAA4C,EAAG,yDAD9C,GAEN,2BAFL;IAGH;IACJ;;IACD,SAAK,MAAMzD,QAAX,IAAuBmD,OAAO,CAAChE,gBAAR,CAAyB,oBAAzB,CAAvB,EAAuE;IACnEc,MAAAA,QAAQ,GAAG,MAAMD,QAAQ,CAAC;IAAE/C,QAAAA,KAAF;IAASqB,QAAAA,OAAT;IAAkB2B,QAAAA;IAAlB,OAAD,CAAzB;IACH;;IACD,WAAOA,QAAP;IACH;;IACD,QAAMqD,cAAN,CAAqBL,YAArB,EAAmCE,OAAnC,EAA4C7E,OAA5C,EAAqDrB,KAArD,EAA4D;IACxD,QAAIgD,QAAJ;IACA,QAAIC,KAAJ;;IACA,QAAI;IACAD,MAAAA,QAAQ,GAAG,MAAMgD,YAAjB;IACH,KAFD,CAGA,OAAO/C,KAAP,EAAc;IAEV;IACA;IACH;;IACD,QAAI;IACA,YAAMiD,OAAO,CAAChD,YAAR,CAAqB,mBAArB,EAA0C;IAC5ClD,QAAAA,KAD4C;IAE5CqB,QAAAA,OAF4C;IAG5C2B,QAAAA;IAH4C,OAA1C,CAAN;IAKA,YAAMkD,OAAO,CAACZ,WAAR,EAAN;IACH,KAPD,CAQA,OAAOmB,cAAP,EAAuB;IACnB,UAAIA,cAAc,YAAYrE,KAA9B,EAAqC;IACjCa,QAAAA,KAAK,GAAGwD,cAAR;IACH;IACJ;;IACD,UAAMP,OAAO,CAAChD,YAAR,CAAqB,oBAArB,EAA2C;IAC7ClD,MAAAA,KAD6C;IAE7CqB,MAAAA,OAF6C;IAG7C2B,MAAAA,QAH6C;IAI7CC,MAAAA,KAAK,EAAEA;IAJsC,KAA3C,CAAN;IAMAiD,IAAAA,OAAO,CAACV,OAAR;;IACA,QAAIvC,KAAJ,EAAW;IACP,YAAMA,KAAN;IACH;IACJ;;IA9LU;IAiMf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;ICnOA;IACA;AACA;IACA;IACA;IACA;IACA;IAIO,MAAMyD,QAAQ,GAAG;IACpBC,EAAAA,aAAa,EAAE,CAACC,YAAD,EAAevF,OAAf,KAA4B,SAAQuF,YAAa,mBAAkBhF,gCAAc,CAACP,OAAO,CAACQ,GAAT,CAAc,GAD1F;IAEpBgF,EAAAA,kBAAkB,EAAG7D,QAAD,IAAc;IAC9B,QAAIA,QAAJ,EAAc;IACVtB,MAAAA,gBAAM,CAACoF,cAAP,CAAuB,+BAAvB;IACApF,MAAAA,gBAAM,CAACC,GAAP,CAAWqB,QAAQ,IAAI,wBAAvB;IACAtB,MAAAA,gBAAM,CAACqF,QAAP;IACH;IACJ;IARmB,CAAjB;;ICVP;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMC,UAAN,SAAyBpB,QAAzB,CAAkC;IAC9B;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,QAAMU,OAAN,CAAcjF,OAAd,EAAuB6E,OAAvB,EAAgC;IAC5B,UAAMe,IAAI,GAAG,EAAb;;IACA,IAA2C;IACvCnH,MAAAA,gBAAM,CAACC,UAAP,CAAkBsB,OAAlB,EAA2B7B,OAA3B,EAAoC;IAChCU,QAAAA,UAAU,EAAE,oBADoB;IAEhCC,QAAAA,SAAS,EAAE,KAAKT,WAAL,CAAiBmF,IAFI;IAGhCzE,QAAAA,QAAQ,EAAE,aAHsB;IAIhCC,QAAAA,SAAS,EAAE;IAJqB,OAApC;IAMH;;IACD,QAAI2C,QAAQ,GAAG,MAAMkD,OAAO,CAAC5C,UAAR,CAAmBjC,OAAnB,CAArB;IACA,QAAI4B,KAAK,GAAGP,SAAZ;;IACA,QAAI,CAACM,QAAL,EAAe;IACX,MAA2C;IACvCiE,QAAAA,IAAI,CAAC5B,IAAL,CAAW,6BAA4B,KAAK5B,SAAU,WAA5C,GACL,sCADL;IAEH;;IACD,UAAI;IACAT,QAAAA,QAAQ,GAAG,MAAMkD,OAAO,CAAC/C,gBAAR,CAAyB9B,OAAzB,CAAjB;IACH,OAFD,CAGA,OAAOc,GAAP,EAAY;IACR,YAAIA,GAAG,YAAYC,KAAnB,EAA0B;IACtBa,UAAAA,KAAK,GAAGd,GAAR;IACH;IACJ;;IACD,MAA2C;IACvC,YAAIa,QAAJ,EAAc;IACViE,UAAAA,IAAI,CAAC5B,IAAL,CAAW,4BAAX;IACH,SAFD,MAGK;IACD4B,UAAAA,IAAI,CAAC5B,IAAL,CAAW,4CAAX;IACH;IACJ;IACJ,KArBD,MAsBK;IACD,MAA2C;IACvC4B,QAAAA,IAAI,CAAC5B,IAAL,CAAW,mCAAkC,KAAK5B,SAAU,UAA5D;IACH;IACJ;;IACD,IAA2C;IACvC/B,MAAAA,gBAAM,CAACoF,cAAP,CAAsBJ,QAAQ,CAACC,aAAT,CAAuB,KAAKjH,WAAL,CAAiBmF,IAAxC,EAA8CxD,OAA9C,CAAtB;;IACA,WAAK,MAAMM,GAAX,IAAkBsF,IAAlB,EAAwB;IACpBvF,QAAAA,gBAAM,CAACC,GAAP,CAAWA,GAAX;IACH;;IACD+E,MAAAA,QAAQ,CAACG,kBAAT,CAA4B7D,QAA5B;IACAtB,MAAAA,gBAAM,CAACqF,QAAP;IACH;;IACD,QAAI,CAAC/D,QAAL,EAAe;IACX,YAAM,IAAIX,4BAAJ,CAAiB,aAAjB,EAAgC;IAAER,QAAAA,GAAG,EAAER,OAAO,CAACQ,GAAf;IAAoBoB,QAAAA;IAApB,OAAhC,CAAN;IACH;;IACD,WAAOD,QAAP;IACH;;IA3D6B;;IC3BlC;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMkE,SAAN,SAAwBtB,QAAxB,CAAiC;IAC7B;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,QAAMU,OAAN,CAAcjF,OAAd,EAAuB6E,OAAvB,EAAgC;IAC5B,IAA2C;IACvCpG,MAAAA,gBAAM,CAACC,UAAP,CAAkBsB,OAAlB,EAA2B7B,OAA3B,EAAoC;IAChCU,QAAAA,UAAU,EAAE,oBADoB;IAEhCC,QAAAA,SAAS,EAAE,KAAKT,WAAL,CAAiBmF,IAFI;IAGhCzE,QAAAA,QAAQ,EAAE,aAHsB;IAIhCC,QAAAA,SAAS,EAAE;IAJqB,OAApC;IAMH;;IACD,UAAM2C,QAAQ,GAAG,MAAMkD,OAAO,CAAC5C,UAAR,CAAmBjC,OAAnB,CAAvB;;IACA,IAA2C;IACvCK,MAAAA,gBAAM,CAACoF,cAAP,CAAsBJ,QAAQ,CAACC,aAAT,CAAuB,KAAKjH,WAAL,CAAiBmF,IAAxC,EAA8CxD,OAA9C,CAAtB;;IACA,UAAI2B,QAAJ,EAAc;IACVtB,QAAAA,gBAAM,CAACC,GAAP,CAAY,mCAAkC,KAAK8B,SAAU,IAAlD,GAAyD,QAApE;IACAiD,QAAAA,QAAQ,CAACG,kBAAT,CAA4B7D,QAA5B;IACH,OAHD,MAIK;IACDtB,QAAAA,gBAAM,CAACC,GAAP,CAAY,6BAA4B,KAAK8B,SAAU,UAAvD;IACH;;IACD/B,MAAAA,gBAAM,CAACqF,QAAP;IACH;;IACD,QAAI,CAAC/D,QAAL,EAAe;IACX,YAAM,IAAIX,4BAAJ,CAAiB,aAAjB,EAAgC;IAAER,QAAAA,GAAG,EAAER,OAAO,CAACQ;IAAf,OAAhC,CAAN;IACH;;IACD,WAAOmB,QAAP;IACH;;IAjC4B;;ICzBjC;IACA;AACA;IACA;IACA;IACA;IACA;IAEO,MAAMmE,sBAAsB,GAAG;IAClC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,eAAe,EAAE,OAAO;IAAEpE,IAAAA;IAAF,GAAP,KAAwB;IACrC,QAAIA,QAAQ,CAACF,MAAT,KAAoB,GAApB,IAA2BE,QAAQ,CAACF,MAAT,KAAoB,CAAnD,EAAsD;IAClD,aAAOE,QAAP;IACH;;IACD,WAAO,IAAP;IACH;IAhBiC,CAA/B;;ICRP;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMqE,YAAN,SAA2BzB,QAA3B,CAAoC;IAChC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIlG,EAAAA,WAAW,CAACE,OAAO,GAAG,EAAX,EAAe;IACtB,UAAMA,OAAN,EADsB;IAGtB;;IACA,QAAI,CAAC,KAAKiB,OAAL,CAAayG,IAAb,CAAmBC,CAAD,IAAO,qBAAqBA,CAA9C,CAAL,EAAuD;IACnD,WAAK1G,OAAL,CAAa2G,OAAb,CAAqBL,sBAArB;IACH;;IACD,SAAKM,sBAAL,GAA8B7H,OAAO,CAAC8H,qBAAR,IAAiC,CAA/D;;IACA,IAA2C;IACvC,UAAI,KAAKD,sBAAT,EAAiC;IAC7B3H,QAAAA,gBAAM,CAAC6H,MAAP,CAAc,KAAKF,sBAAnB,EAA2C,QAA3C,EAAqD;IACjDvH,UAAAA,UAAU,EAAE,oBADqC;IAEjDC,UAAAA,SAAS,EAAE,KAAKT,WAAL,CAAiBmF,IAFqB;IAGjDzE,UAAAA,QAAQ,EAAE,aAHuC;IAIjDC,UAAAA,SAAS,EAAE;IAJsC,SAArD;IAMH;IACJ;IACJ;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMiG,OAAN,CAAcjF,OAAd,EAAuB6E,OAAvB,EAAgC;IAC5B,UAAMe,IAAI,GAAG,EAAb;;IACA,IAA2C;IACvCnH,MAAAA,gBAAM,CAACC,UAAP,CAAkBsB,OAAlB,EAA2B7B,OAA3B,EAAoC;IAChCU,QAAAA,UAAU,EAAE,oBADoB;IAEhCC,QAAAA,SAAS,EAAE,KAAKT,WAAL,CAAiBmF,IAFI;IAGhCzE,QAAAA,QAAQ,EAAE,QAHsB;IAIhCC,QAAAA,SAAS,EAAE;IAJqB,OAApC;IAMH;;IACD,UAAMuH,QAAQ,GAAG,EAAjB;IACA,QAAIC,SAAJ;;IACA,QAAI,KAAKJ,sBAAT,EAAiC;IAC7B,YAAM;IAAEK,QAAAA,EAAF;IAAM3G,QAAAA;IAAN,UAAkB,KAAK4G,kBAAL,CAAwB;IAAE1G,QAAAA,OAAF;IAAW4F,QAAAA,IAAX;IAAiBf,QAAAA;IAAjB,OAAxB,CAAxB;;IACA2B,MAAAA,SAAS,GAAGC,EAAZ;IACAF,MAAAA,QAAQ,CAACvC,IAAT,CAAclE,OAAd;IACH;;IACD,UAAM6G,cAAc,GAAG,KAAKC,kBAAL,CAAwB;IAC3CJ,MAAAA,SAD2C;IAE3CxG,MAAAA,OAF2C;IAG3C4F,MAAAA,IAH2C;IAI3Cf,MAAAA;IAJ2C,KAAxB,CAAvB;;IAMA0B,IAAAA,QAAQ,CAACvC,IAAT,CAAc2C,cAAd;IACA,UAAMhF,QAAQ,GAAG,MAAMkD,OAAO,CAAChF,SAAR,CAAkB,CAAC,YAAY;IAClD;IACA,aAAQ,CAAC,MAAMgF,OAAO,CAAChF,SAAR,CAAkBgH,OAAO,CAACC,IAAR,CAAaP,QAAb,CAAlB,CAAP;IAEJ;IACA;IACA;IACA;IACC,YAAMI,cANH,CAAR;IAOH,KATwC,GAAlB,CAAvB;;IAUA,IAA2C;IACvCtG,MAAAA,gBAAM,CAACoF,cAAP,CAAsBJ,QAAQ,CAACC,aAAT,CAAuB,KAAKjH,WAAL,CAAiBmF,IAAxC,EAA8CxD,OAA9C,CAAtB;;IACA,WAAK,MAAMM,GAAX,IAAkBsF,IAAlB,EAAwB;IACpBvF,QAAAA,gBAAM,CAACC,GAAP,CAAWA,GAAX;IACH;;IACD+E,MAAAA,QAAQ,CAACG,kBAAT,CAA4B7D,QAA5B;IACAtB,MAAAA,gBAAM,CAACqF,QAAP;IACH;;IACD,QAAI,CAAC/D,QAAL,EAAe;IACX,YAAM,IAAIX,4BAAJ,CAAiB,aAAjB,EAAgC;IAAER,QAAAA,GAAG,EAAER,OAAO,CAACQ;IAAf,OAAhC,CAAN;IACH;;IACD,WAAOmB,QAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI+E,EAAAA,kBAAkB,CAAC;IAAE1G,IAAAA,OAAF;IAAW4F,IAAAA,IAAX;IAAiBf,IAAAA;IAAjB,GAAD,EAA8B;IAC5C,QAAI2B,SAAJ;IACA,UAAMO,cAAc,GAAG,IAAIF,OAAJ,CAAazC,OAAD,IAAa;IAC5C,YAAM4C,gBAAgB,GAAG,YAAY;IACjC,QAA2C;IACvCpB,UAAAA,IAAI,CAAC5B,IAAL,CAAW,qCAAD,GACL,GAAE,KAAKoC,sBAAuB,WADnC;IAEH;;IACDhC,QAAAA,OAAO,CAAC,MAAMS,OAAO,CAAC5C,UAAR,CAAmBjC,OAAnB,CAAP,CAAP;IACH,OAND;;IAOAwG,MAAAA,SAAS,GAAGS,UAAU,CAACD,gBAAD,EAAmB,KAAKZ,sBAAL,GAA8B,IAAjD,CAAtB;IACH,KATsB,CAAvB;IAUA,WAAO;IACHtG,MAAAA,OAAO,EAAEiH,cADN;IAEHN,MAAAA,EAAE,EAAED;IAFD,KAAP;IAIH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMI,kBAAN,CAAyB;IAAEJ,IAAAA,SAAF;IAAaxG,IAAAA,OAAb;IAAsB4F,IAAAA,IAAtB;IAA4Bf,IAAAA;IAA5B,GAAzB,EAAiE;IAC7D,QAAIjD,KAAJ;IACA,QAAID,QAAJ;;IACA,QAAI;IACAA,MAAAA,QAAQ,GAAG,MAAMkD,OAAO,CAAC/C,gBAAR,CAAyB9B,OAAzB,CAAjB;IACH,KAFD,CAGA,OAAOkH,UAAP,EAAmB;IACf,UAAIA,UAAU,YAAYnG,KAA1B,EAAiC;IAC7Ba,QAAAA,KAAK,GAAGsF,UAAR;IACH;IACJ;;IACD,QAAIV,SAAJ,EAAe;IACXW,MAAAA,YAAY,CAACX,SAAD,CAAZ;IACH;;IACD,IAA2C;IACvC,UAAI7E,QAAJ,EAAc;IACViE,QAAAA,IAAI,CAAC5B,IAAL,CAAW,4BAAX;IACH,OAFD,MAGK;IACD4B,QAAAA,IAAI,CAAC5B,IAAL,CAAW,0DAAD,GACL,yBADL;IAEH;IACJ;;IACD,QAAIpC,KAAK,IAAI,CAACD,QAAd,EAAwB;IACpBA,MAAAA,QAAQ,GAAG,MAAMkD,OAAO,CAAC5C,UAAR,CAAmBjC,OAAnB,CAAjB;;IACA,MAA2C;IACvC,YAAI2B,QAAJ,EAAc;IACViE,UAAAA,IAAI,CAAC5B,IAAL,CAAW,mCAAkC,KAAK5B,SAAU,GAAlD,GAAwD,SAAlE;IACH,SAFD,MAGK;IACDwD,UAAAA,IAAI,CAAC5B,IAAL,CAAW,6BAA4B,KAAK5B,SAAU,UAAtD;IACH;IACJ;IACJ;;IACD,WAAOT,QAAP;IACH;;IApK+B;;IC9BpC;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMyF,WAAN,SAA0B7C,QAA1B,CAAmC;IAC/B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIlG,EAAAA,WAAW,CAACE,OAAO,GAAG,EAAX,EAAe;IACtB,UAAMA,OAAN;IACA,SAAK6H,sBAAL,GAA8B7H,OAAO,CAAC8H,qBAAR,IAAiC,CAA/D;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMpB,OAAN,CAAcjF,OAAd,EAAuB6E,OAAvB,EAAgC;IAC5B,IAA2C;IACvCpG,MAAAA,gBAAM,CAACC,UAAP,CAAkBsB,OAAlB,EAA2B7B,OAA3B,EAAoC;IAChCU,QAAAA,UAAU,EAAE,oBADoB;IAEhCC,QAAAA,SAAS,EAAE,KAAKT,WAAL,CAAiBmF,IAFI;IAGhCzE,QAAAA,QAAQ,EAAE,SAHsB;IAIhCC,QAAAA,SAAS,EAAE;IAJqB,OAApC;IAMH;;IACD,QAAI4C,KAAK,GAAGP,SAAZ;IACA,QAAIM,QAAJ;;IACA,QAAI;IACA,YAAM4E,QAAQ,GAAG,CACb1B,OAAO,CAAC9E,KAAR,CAAcC,OAAd,CADa,CAAjB;;IAGA,UAAI,KAAKoG,sBAAT,EAAiC;IAC7B,cAAMW,cAAc,GAAGpE,kBAAO,CAAC,KAAKyD,sBAAL,GAA8B,IAA/B,CAA9B;IACAG,QAAAA,QAAQ,CAACvC,IAAT,CAAc+C,cAAd;IACH;;IACDpF,MAAAA,QAAQ,GAAG,MAAMkF,OAAO,CAACC,IAAR,CAAaP,QAAb,CAAjB;;IACA,UAAI,CAAC5E,QAAL,EAAe;IACX,cAAM,IAAIZ,KAAJ,CAAW,uCAAD,GACX,GAAE,KAAKqF,sBAAuB,WAD7B,CAAN;IAEH;IACJ,KAbD,CAcA,OAAOtF,GAAP,EAAY;IACR,UAAIA,GAAG,YAAYC,KAAnB,EAA0B;IACtBa,QAAAA,KAAK,GAAGd,GAAR;IACH;IACJ;;IACD,IAA2C;IACvCT,MAAAA,gBAAM,CAACoF,cAAP,CAAsBJ,QAAQ,CAACC,aAAT,CAAuB,KAAKjH,WAAL,CAAiBmF,IAAxC,EAA8CxD,OAA9C,CAAtB;;IACA,UAAI2B,QAAJ,EAAc;IACVtB,QAAAA,gBAAM,CAACC,GAAP,CAAY,4BAAZ;IACH,OAFD,MAGK;IACDD,QAAAA,gBAAM,CAACC,GAAP,CAAY,4CAAZ;IACH;;IACD+E,MAAAA,QAAQ,CAACG,kBAAT,CAA4B7D,QAA5B;IACAtB,MAAAA,gBAAM,CAACqF,QAAP;IACH;;IACD,QAAI,CAAC/D,QAAL,EAAe;IACX,YAAM,IAAIX,4BAAJ,CAAiB,aAAjB,EAAgC;IAAER,QAAAA,GAAG,EAAER,OAAO,CAACQ,GAAf;IAAoBoB,QAAAA;IAApB,OAAhC,CAAN;IACH;;IACD,WAAOD,QAAP;IACH;;IApE8B;;IC3BnC;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAM0F,oBAAN,SAAmC9C,QAAnC,CAA4C;IACxC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIlG,EAAAA,WAAW,CAACE,OAAO,GAAG,EAAX,EAAe;IACtB,UAAMA,OAAN,EADsB;IAGtB;;IACA,QAAI,CAAC,KAAKiB,OAAL,CAAayG,IAAb,CAAmBC,CAAD,IAAO,qBAAqBA,CAA9C,CAAL,EAAuD;IACnD,WAAK1G,OAAL,CAAa2G,OAAb,CAAqBL,sBAArB;IACH;IACJ;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMb,OAAN,CAAcjF,OAAd,EAAuB6E,OAAvB,EAAgC;IAC5B,UAAMe,IAAI,GAAG,EAAb;;IACA,IAA2C;IACvCnH,MAAAA,gBAAM,CAACC,UAAP,CAAkBsB,OAAlB,EAA2B7B,OAA3B,EAAoC;IAChCU,QAAAA,UAAU,EAAE,oBADoB;IAEhCC,QAAAA,SAAS,EAAE,KAAKT,WAAL,CAAiBmF,IAFI;IAGhCzE,QAAAA,QAAQ,EAAE,QAHsB;IAIhCC,QAAAA,SAAS,EAAE;IAJqB,OAApC;IAMH;;IACD,UAAMsI,oBAAoB,GAAGzC,OAAO,CAAC/C,gBAAR,CAAyB9B,OAAzB,EAAkCuH,KAAlC,CAAwC,MAAM;IAEvE;IACH,KAH4B,CAA7B;IAIA,SAAK1C,OAAO,CAAChF,SAAR,CAAkByH,oBAAlB,CAAL;IACA,QAAI3F,QAAQ,GAAG,MAAMkD,OAAO,CAAC5C,UAAR,CAAmBjC,OAAnB,CAArB;IACA,QAAI4B,KAAJ;;IACA,QAAID,QAAJ,EAAc;IACV,MAA2C;IACvCiE,QAAAA,IAAI,CAAC5B,IAAL,CAAW,mCAAkC,KAAK5B,SAAU,GAAlD,GACL,kEADL;IAEH;IACJ,KALD,MAMK;IACD,MAA2C;IACvCwD,QAAAA,IAAI,CAAC5B,IAAL,CAAW,6BAA4B,KAAK5B,SAAU,WAA5C,GACL,qCADL;IAEH;;IACD,UAAI;IACA;IACA;IACAT,QAAAA,QAAQ,GAAI,MAAM2F,oBAAlB;IACH,OAJD,CAKA,OAAOxG,GAAP,EAAY;IACR,YAAIA,GAAG,YAAYC,KAAnB,EAA0B;IACtBa,UAAAA,KAAK,GAAGd,GAAR;IACH;IACJ;IACJ;;IACD,IAA2C;IACvCT,MAAAA,gBAAM,CAACoF,cAAP,CAAsBJ,QAAQ,CAACC,aAAT,CAAuB,KAAKjH,WAAL,CAAiBmF,IAAxC,EAA8CxD,OAA9C,CAAtB;;IACA,WAAK,MAAMM,GAAX,IAAkBsF,IAAlB,EAAwB;IACpBvF,QAAAA,gBAAM,CAACC,GAAP,CAAWA,GAAX;IACH;;IACD+E,MAAAA,QAAQ,CAACG,kBAAT,CAA4B7D,QAA5B;IACAtB,MAAAA,gBAAM,CAACqF,QAAP;IACH;;IACD,QAAI,CAAC/D,QAAL,EAAe;IACX,YAAM,IAAIX,4BAAJ,CAAiB,aAAjB,EAAgC;IAAER,QAAAA,GAAG,EAAER,OAAO,CAACQ,GAAf;IAAoBoB,QAAAA;IAApB,OAAhC,CAAN;IACH;;IACD,WAAOD,QAAP;IACH;;IAhFuC;;;;;;;;;;;;;;;;"}