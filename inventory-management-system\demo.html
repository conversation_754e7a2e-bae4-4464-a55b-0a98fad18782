<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory Management System - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">🏪 Inventory Management System</h1>
                <p class="text-gray-600">Demo Interface - Backend API Testing</p>
                <div class="mt-4 p-3 rounded-md bg-green-50 border border-green-200">
                    <span class="text-green-800">🔑 <strong>Default Admin:</strong> username=<code>admin</code>, password=<code>admin123</code></span>
                </div>
                <div id="api-status" class="mt-4 p-3 rounded-md bg-blue-50 border border-blue-200">
                    <span class="text-blue-800">🔄 Checking API connection...</span>
                </div>
            </div>

            <!-- Authentication Section -->
            <div class="grid md:grid-cols-2 gap-6 mb-6">
                <!-- Register -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold mb-4">👤 Register New User</h2>
                    <form id="registerForm" class="space-y-4">
                        <input type="text" id="regUsername" placeholder="Username" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="password" id="regPassword" placeholder="Password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <select id="regRole" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="employee">Employee</option>
                            <option value="admin">Admin</option>
                        </select>
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-200">
                            Register
                        </button>
                    </form>
                    <div id="registerResult" class="mt-4"></div>
                </div>

                <!-- Login -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold mb-4">🔐 Login</h2>
                    <form id="loginForm" class="space-y-4">
                        <input type="text" id="loginUsername" placeholder="Username" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="password" id="loginPassword" placeholder="Password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition duration-200">
                            Login
                        </button>
                    </form>
                    <div id="loginResult" class="mt-4"></div>
                </div>
            </div>

            <!-- Product Management Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6" id="productSection" style="display: none;">
                <h2 class="text-xl font-semibold mb-4">📦 Product Management</h2>
                
                <!-- Add Product Form -->
                <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h3 class="text-lg font-medium mb-3">Add New Product</h3>
                    <form id="productForm" class="grid md:grid-cols-2 gap-4">
                        <input type="text" id="productName" placeholder="Product Name" required 
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="text" id="productCategory" placeholder="Category" 
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="number" id="productPrice" placeholder="Price" step="0.01" required 
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="number" id="productQuantity" placeholder="Quantity" 
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <textarea id="productDescription" placeholder="Description" 
                                  class="md:col-span-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        <button type="submit" class="md:col-span-2 bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition duration-200">
                            Add Product
                        </button>
                    </form>
                    <div id="productResult" class="mt-4"></div>
                </div>

                <!-- Products List -->
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium">Products List</h3>
                        <button id="loadProducts" class="bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition duration-200">
                            Refresh Products
                        </button>
                    </div>
                    <div id="productsList" class="space-y-2"></div>
                </div>
            </div>

            <!-- User Management Section (Admin Only) -->
            <div id="userManagement" class="bg-white rounded-lg shadow-md p-6 mb-6" style="display: none;">
                <h2 class="text-xl font-semibold mb-4">👥 User Management (Admin Only)</h2>
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium">All Users</h3>
                    <button id="loadUsers" class="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-200">
                        Refresh Users
                    </button>
                </div>
                <div id="usersList" class="space-y-2"></div>
            </div>

            <!-- User Info -->
            <div id="userInfo" class="bg-white rounded-lg shadow-md p-6" style="display: none;">
                <h2 class="text-xl font-semibold mb-4">👨‍💼 User Information</h2>
                <div id="userDetails"></div>
                <button id="logoutBtn" class="mt-4 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition duration-200">
                    Logout
                </button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let authToken = localStorage.getItem('token');
        let currentUser = JSON.parse(localStorage.getItem('user') || 'null');

        // Set up axios defaults
        if (authToken) {
            axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
        }

        // Check API status
        async function checkApiStatus() {
            try {
                const response = await axios.get('http://localhost:5000/');
                document.getElementById('api-status').innerHTML = 
                    `<span class="text-green-800">✅ API Connected: ${response.data.message}</span>`;
                
                if (authToken && currentUser) {
                    showLoggedInState();
                }
            } catch (error) {
                document.getElementById('api-status').innerHTML = 
                    `<span class="text-red-800">❌ API Connection Failed: ${error.message}</span>`;
            }
        }

        // Register user
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('regUsername').value;
            const password = document.getElementById('regPassword').value;
            const role = document.getElementById('regRole').value;

            try {
                const response = await axios.post(`${API_BASE}/auth/register`, {
                    username, password, role
                });
                document.getElementById('registerResult').innerHTML = 
                    `<div class="p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                        ✅ ${response.data.message}
                    </div>`;
                document.getElementById('registerForm').reset();
            } catch (error) {
                document.getElementById('registerResult').innerHTML = 
                    `<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        ❌ ${error.response?.data?.error || error.message}
                    </div>`;
            }
        });

        // Login user
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await axios.post(`${API_BASE}/auth/login`, {
                    username, password
                });
                
                authToken = response.data.token;
                currentUser = response.data.user;
                
                localStorage.setItem('token', authToken);
                localStorage.setItem('user', JSON.stringify(currentUser));
                axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;

                document.getElementById('loginResult').innerHTML = 
                    `<div class="p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                        ✅ Login successful!
                    </div>`;
                
                showLoggedInState();
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        ❌ ${error.response?.data?.error || error.message}
                    </div>`;
            }
        });

        // Show logged in state
        function showLoggedInState() {
            document.getElementById('productSection').style.display = 'block';
            document.getElementById('userInfo').style.display = 'block';
            document.getElementById('userDetails').innerHTML =
                `<p><strong>Username:</strong> ${currentUser.username}</p>
                 <p><strong>Role:</strong> ${currentUser.role}</p>
                 <p><strong>ID:</strong> ${currentUser.id}</p>`;

            // Show user management for admins
            if (currentUser.role === 'admin') {
                document.getElementById('userManagement').style.display = 'block';
                loadUsers();
            }
        }

        // Add product
        document.getElementById('productForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const productData = {
                name: document.getElementById('productName').value,
                category: document.getElementById('productCategory').value,
                price: parseFloat(document.getElementById('productPrice').value),
                quantity: parseInt(document.getElementById('productQuantity').value) || 0,
                description: document.getElementById('productDescription').value
            };

            try {
                const response = await axios.post(`${API_BASE}/products`, productData);
                document.getElementById('productResult').innerHTML = 
                    `<div class="p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                        ✅ ${response.data.message}
                    </div>`;
                document.getElementById('productForm').reset();
                loadProducts();
            } catch (error) {
                document.getElementById('productResult').innerHTML = 
                    `<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        ❌ ${error.response?.data?.error || error.message}
                    </div>`;
            }
        });

        // Load products
        async function loadProducts() {
            try {
                const response = await axios.get(`${API_BASE}/products`);
                const products = response.data;
                
                if (products.length === 0) {
                    document.getElementById('productsList').innerHTML = 
                        '<p class="text-gray-500 text-center py-4">No products found. Add some products to get started!</p>';
                    return;
                }

                document.getElementById('productsList').innerHTML = products.map(product => 
                    `<div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="font-semibold text-lg">${product.name}</h4>
                                <p class="text-gray-600">${product.description || 'No description'}</p>
                                <p class="text-sm text-gray-500">Category: ${product.category || 'N/A'}</p>
                                <p class="text-sm text-gray-500">Supplier: ${product.supplier_name || 'N/A'}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-green-600">$${product.price}</p>
                                <p class="text-sm ${product.quantity < 10 ? 'text-red-600' : 'text-gray-600'}">
                                    Stock: ${product.quantity}
                                </p>
                            </div>
                        </div>
                    </div>`
                ).join('');
            } catch (error) {
                document.getElementById('productsList').innerHTML = 
                    `<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        ❌ Failed to load products: ${error.response?.data?.error || error.message}
                    </div>`;
            }
        }

        // Load users (admin only)
        async function loadUsers() {
            try {
                const response = await axios.get(`${API_BASE}/users`);
                const users = response.data;

                document.getElementById('usersList').innerHTML = users.map(user =>
                    `<div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <div>
                                <h4 class="font-semibold">${user.username}</h4>
                                <p class="text-sm text-gray-600">Role: ${user.role}</p>
                                <p class="text-sm text-gray-500">ID: ${user.id}</p>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                                }">
                                    ${user.role}
                                </span>
                            </div>
                        </div>
                    </div>`
                ).join('');
            } catch (error) {
                document.getElementById('usersList').innerHTML =
                    `<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        ❌ Failed to load users: ${error.response?.data?.error || error.message}
                    </div>`;
            }
        }

        // Load products button
        document.getElementById('loadProducts').addEventListener('click', loadProducts);

        // Load users button
        document.getElementById('loadUsers').addEventListener('click', loadUsers);

        // Logout
        document.getElementById('logoutBtn').addEventListener('click', () => {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            delete axios.defaults.headers.common['Authorization'];
            authToken = null;
            currentUser = null;

            document.getElementById('productSection').style.display = 'none';
            document.getElementById('userInfo').style.display = 'none';
            document.getElementById('userManagement').style.display = 'none';
            document.getElementById('loginResult').innerHTML =
                `<div class="p-3 bg-blue-100 border border-blue-400 text-blue-700 rounded">
                    ℹ️ Logged out successfully
                </div>`;
        });

        // Initialize
        checkApiStatus();
    </script>
</body>
</html>
