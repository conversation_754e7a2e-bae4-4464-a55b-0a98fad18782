<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StockFlow - Modern Inventory Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .glass { backdrop-filter: blur(16px); background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); }
        .animate-fade-in { animation: fadeIn 0.6s ease-out; }
        .animate-slide-up { animation: slideUp 0.5s ease-out; }
        .animate-bounce-in { animation: bounceIn 0.8s ease-out; }
        .hover-scale { transition: transform 0.3s ease; }
        .hover-scale:hover { transform: scale(1.05); }
        .card-shadow { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        @keyframes slideUp { from { transform: translateY(30px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        .floating { animation: floating 3s ease-in-out infinite; }
        @keyframes floating { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }

        .pulse-ring { animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite; }
        @keyframes pulse-ring { 0% { transform: scale(0.33); } 40%, 50% { opacity: 0; } 100% { opacity: 0; transform: scale(1.33); } }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-10 left-10 w-72 h-72 bg-white opacity-10 rounded-full floating"></div>
        <div class="absolute top-1/2 right-10 w-96 h-96 bg-white opacity-5 rounded-full floating" style="animation-delay: -1s;"></div>
        <div class="absolute bottom-10 left-1/3 w-64 h-64 bg-white opacity-10 rounded-full floating" style="animation-delay: -2s;"></div>
    </div>

    <!-- Navigation -->
    <nav class="glass fixed top-0 left-0 right-0 z-50 animate-fade-in">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                        <i data-lucide="package" class="w-6 h-6 text-purple-600"></i>
                    </div>
                    <h1 class="text-xl font-bold text-white">StockFlow</h1>
                </div>
                <div id="navUser" class="hidden items-center space-x-4">
                    <div class="text-white text-sm">
                        <span id="navUsername"></span>
                        <span id="navRole" class="ml-2 px-2 py-1 bg-white bg-opacity-20 rounded-full text-xs"></span>
                    </div>
                    <button id="navLogout" class="text-white hover:text-gray-200 transition-colors">
                        <i data-lucide="log-out" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-20 pb-10 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">

            <!-- Welcome Section -->
            <div id="welcomeSection" class="text-center mb-12 animate-bounce-in">
                <div class="relative inline-block">
                    <div class="absolute inset-0 bg-white opacity-20 rounded-full pulse-ring"></div>
                    <div class="relative w-24 h-24 bg-white rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="trending-up" class="w-12 h-12 text-purple-600"></i>
                    </div>
                </div>
                <h1 class="text-5xl font-bold text-white mb-4">Welcome to StockFlow</h1>
                <p class="text-xl text-white opacity-90 mb-8">Modern Inventory Management System</p>
                <div class="flex justify-center space-x-4">
                    <button id="loginBtn" class="bg-white text-purple-600 px-8 py-3 rounded-full font-semibold hover-scale shadow-lg">
                        Get Started
                    </button>
                    <button id="demoBtn" class="glass text-white px-8 py-3 rounded-full font-semibold hover-scale">
                        View Demo
                    </button>
                </div>
            </div>

            <!-- Login Modal -->
            <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 animate-slide-up">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="lock" class="w-8 h-8 text-purple-600"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Welcome Back</h2>
                        <p class="text-gray-600">Sign in to your account</p>
                    </div>

                    <!-- Quick Login Hint -->
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-3 mb-6">
                        <div class="flex items-center space-x-2">
                            <i data-lucide="info" class="w-4 h-4 text-purple-600"></i>
                            <span class="text-sm text-purple-800">
                                <strong>Quick Login:</strong> admin / admin123
                            </span>
                        </div>
                    </div>

                    <form id="loginForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input type="text" id="loginUsername" value="admin" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input type="password" id="loginPassword" value="admin123" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                        </div>
                        <button type="submit" class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition duration-200 font-semibold">
                            Sign In
                        </button>
                    </form>

                    <div class="mt-6 text-center">
                        <button id="showRegister" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                            Don't have an account? Register here
                        </button>
                    </div>

                    <button id="closeLogin" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>

                    <div id="loginResult" class="mt-4"></div>
                </div>
            </div>

            <!-- Register Modal -->
            <div id="registerModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 animate-slide-up">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="user-plus" class="w-8 h-8 text-green-600"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Create Account</h2>
                        <p class="text-gray-600">Join our inventory system</p>
                    </div>

                    <form id="registerForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input type="text" id="regUsername" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input type="password" id="regPassword" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                            <select id="regRole" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                                <option value="employee">Employee</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        <button type="submit" class="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition duration-200 font-semibold">
                            Create Account
                        </button>
                    </form>

                    <div class="mt-6 text-center">
                        <button id="showLogin" class="text-green-600 hover:text-green-700 text-sm font-medium">
                            Already have an account? Sign in
                        </button>
                    </div>

                    <button id="closeRegister" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>

                    <div id="registerResult" class="mt-4"></div>
                </div>
            </div>

            <!-- Dashboard Section -->
            <div id="dashboardSection" class="hidden">

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="glass rounded-2xl p-6 text-white hover-scale animate-slide-up">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white opacity-80 text-sm">Total Products</p>
                                <p id="totalProducts" class="text-3xl font-bold">0</p>
                            </div>
                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                <i data-lucide="package" class="w-6 h-6"></i>
                            </div>
                        </div>
                    </div>

                    <div class="glass rounded-2xl p-6 text-white hover-scale animate-slide-up" style="animation-delay: 0.1s;">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white opacity-80 text-sm">Total Stock</p>
                                <p id="totalStock" class="text-3xl font-bold">0</p>
                            </div>
                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                <i data-lucide="trending-up" class="w-6 h-6"></i>
                            </div>
                        </div>
                    </div>

                    <div class="glass rounded-2xl p-6 text-white hover-scale animate-slide-up" style="animation-delay: 0.2s;">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white opacity-80 text-sm">Low Stock</p>
                                <p id="lowStock" class="text-3xl font-bold">0</p>
                            </div>
                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                <i data-lucide="alert-triangle" class="w-6 h-6"></i>
                            </div>
                        </div>
                    </div>

                    <div class="glass rounded-2xl p-6 text-white hover-scale animate-slide-up" style="animation-delay: 0.3s;">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white opacity-80 text-sm">Total Users</p>
                                <p id="totalUsers" class="text-3xl font-bold">0</p>
                            </div>
                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                <i data-lucide="users" class="w-6 h-6"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-4 mb-8">
                    <button id="addProductBtn" class="bg-white text-purple-600 px-6 py-3 rounded-xl font-semibold hover-scale shadow-lg flex items-center space-x-2">
                        <i data-lucide="plus" class="w-5 h-5"></i>
                        <span>Add Product</span>
                    </button>
                    <button id="viewProductsBtn" class="glass text-white px-6 py-3 rounded-xl font-semibold hover-scale flex items-center space-x-2">
                        <i data-lucide="eye" class="w-5 h-5"></i>
                        <span>View Products</span>
                    </button>
                    <button id="manageUsersBtn" class="glass text-white px-6 py-3 rounded-xl font-semibold hover-scale flex items-center space-x-2">
                        <i data-lucide="users" class="w-5 h-5"></i>
                        <span>Manage Users</span>
                    </button>
                    <button id="refreshDataBtn" class="glass text-white px-6 py-3 rounded-xl font-semibold hover-scale flex items-center space-x-2">
                        <i data-lucide="refresh-cw" class="w-5 h-5"></i>
                        <span>Refresh</span>
                    </button>
                </div>

                <!-- Content Sections -->
                <div class="space-y-8">

                    <!-- Add Product Section -->
                    <div id="addProductSection" class="bg-white rounded-2xl p-8 card-shadow hidden animate-slide-up">
                        <div class="flex items-center space-x-3 mb-6">
                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="plus" class="w-5 h-5 text-purple-600"></i>
                            </div>
                            <h2 class="text-2xl font-bold text-gray-900">Add New Product</h2>
                        </div>

                        <form id="productForm" class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Product Name</label>
                                <input type="text" id="productName" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                <input type="text" id="productCategory"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Price ($)</label>
                                <input type="number" id="productPrice" step="0.01" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                                <input type="number" id="productQuantity"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                <textarea id="productDescription" rows="3"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"></textarea>
                            </div>
                            <div class="md:col-span-2">
                                <button type="submit" class="bg-purple-600 text-white py-3 px-8 rounded-lg hover:bg-purple-700 transition duration-200 font-semibold flex items-center space-x-2">
                                    <i data-lucide="plus" class="w-5 h-5"></i>
                                    <span>Add Product</span>
                                </button>
                            </div>
                        </form>
                        <div id="productResult" class="mt-6"></div>
                    </div>

                    <!-- Products View Section -->
                    <div id="productsViewSection" class="bg-white rounded-2xl p-8 card-shadow hidden animate-slide-up">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="package" class="w-5 h-5 text-blue-600"></i>
                                </div>
                                <h2 class="text-2xl font-bold text-gray-900">Products Inventory</h2>
                            </div>
                            <button id="loadProducts" class="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-200 flex items-center space-x-2">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                <span>Refresh</span>
                            </button>
                        </div>
                        <div id="productsList" class="grid gap-4"></div>
                    </div>

                    <!-- User Management Section -->
                    <div id="userManagementSection" class="bg-white rounded-2xl p-8 card-shadow hidden animate-slide-up">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="users" class="w-5 h-5 text-green-600"></i>
                                </div>
                                <h2 class="text-2xl font-bold text-gray-900">User Management</h2>
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Admin Only</span>
                            </div>
                            <button id="loadUsers" class="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition duration-200 flex items-center space-x-2">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                <span>Refresh</span>
                            </button>
                        </div>
                        <div id="usersList" class="grid gap-4"></div>
                    </div>

                </div>
            </div>

            <!-- Success/Error Toast -->
            <div id="toast" class="fixed top-20 right-4 z-50 hidden">
                <div class="bg-white rounded-lg shadow-lg p-4 max-w-sm">
                    <div class="flex items-center space-x-3">
                        <div id="toastIcon" class="w-8 h-8 rounded-full flex items-center justify-center">
                            <i data-lucide="check" class="w-5 h-5"></i>
                        </div>
                        <div>
                            <p id="toastTitle" class="font-semibold text-gray-900"></p>
                            <p id="toastMessage" class="text-sm text-gray-600"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        const API_BASE = 'http://localhost:5000/api';
        let authToken = localStorage.getItem('token');
        let currentUser = JSON.parse(localStorage.getItem('user') || 'null');
        let products = [];
        let users = [];

        // Set up axios defaults
        if (authToken) {
            axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            checkApiStatus();
            setupEventListeners();

            if (authToken && currentUser) {
                showDashboard();
            }
        });

        // Setup event listeners
        function setupEventListeners() {
            // Navigation buttons
            document.getElementById('loginBtn').addEventListener('click', () => showModal('loginModal'));
            document.getElementById('demoBtn').addEventListener('click', () => showModal('loginModal'));

            // Modal controls
            document.getElementById('closeLogin').addEventListener('click', () => hideModal('loginModal'));
            document.getElementById('closeRegister').addEventListener('click', () => hideModal('registerModal'));
            document.getElementById('showRegister').addEventListener('click', () => {
                hideModal('loginModal');
                showModal('registerModal');
            });
            document.getElementById('showLogin').addEventListener('click', () => {
                hideModal('registerModal');
                showModal('loginModal');
            });

            // Dashboard buttons
            document.getElementById('addProductBtn').addEventListener('click', () => toggleSection('addProductSection'));
            document.getElementById('viewProductsBtn').addEventListener('click', () => {
                toggleSection('productsViewSection');
                loadProducts();
            });
            document.getElementById('manageUsersBtn').addEventListener('click', () => {
                if (currentUser?.role === 'admin') {
                    toggleSection('userManagementSection');
                    loadUsers();
                } else {
                    showToast('Access Denied', 'Admin privileges required', 'error');
                }
            });
            document.getElementById('refreshDataBtn').addEventListener('click', refreshDashboard);

            // Navigation logout
            document.getElementById('navLogout').addEventListener('click', logout);

            // Forms
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            document.getElementById('registerForm').addEventListener('submit', handleRegister);
            document.getElementById('productForm').addEventListener('submit', handleAddProduct);
            document.getElementById('loadProducts').addEventListener('click', loadProducts);
            document.getElementById('loadUsers').addEventListener('click', loadUsers);
        }

        // Modal functions
        function showModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function hideModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Section toggle
        function toggleSection(sectionId) {
            // Hide all sections
            ['addProductSection', 'productsViewSection', 'userManagementSection'].forEach(id => {
                document.getElementById(id).classList.add('hidden');
            });

            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');
        }

        // Check API status
        async function checkApiStatus() {
            try {
                const response = await axios.get('http://localhost:5000/');
                console.log('API Connected:', response.data.message);
            } catch (error) {
                showToast('Connection Error', 'Failed to connect to API', 'error');
            }
        }

        // Handle login
        async function handleLogin(e) {
            e.preventDefault();
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await axios.post(`${API_BASE}/auth/login`, {
                    username, password
                });

                authToken = response.data.token;
                currentUser = response.data.user;

                localStorage.setItem('token', authToken);
                localStorage.setItem('user', JSON.stringify(currentUser));
                axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;

                hideModal('loginModal');
                showDashboard();
                showToast('Welcome!', `Logged in as ${currentUser.username}`, 'success');
            } catch (error) {
                document.getElementById('loginResult').innerHTML =
                    `<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        ❌ ${error.response?.data?.error || error.message}
                    </div>`;
            }
        }

        // Handle register
        async function handleRegister(e) {
            e.preventDefault();
            const username = document.getElementById('regUsername').value;
            const password = document.getElementById('regPassword').value;
            const role = document.getElementById('regRole').value;

            try {
                const response = await axios.post(`${API_BASE}/auth/register`, {
                    username, password, role
                });

                showToast('Success!', 'Account created successfully', 'success');
                document.getElementById('registerForm').reset();

                // Auto switch to login
                setTimeout(() => {
                    hideModal('registerModal');
                    showModal('loginModal');
                }, 1500);
            } catch (error) {
                document.getElementById('registerResult').innerHTML =
                    `<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        ❌ ${error.response?.data?.error || error.message}
                    </div>`;
            }
        }

        // Show dashboard
        function showDashboard() {
            document.getElementById('welcomeSection').style.display = 'none';
            document.getElementById('dashboardSection').classList.remove('hidden');
            document.getElementById('navUser').classList.remove('hidden');
            document.getElementById('navUser').classList.add('flex');

            // Update nav user info
            document.getElementById('navUsername').textContent = currentUser.username;
            document.getElementById('navRole').textContent = currentUser.role;

            refreshDashboard();
        }

        // Refresh dashboard data
        async function refreshDashboard() {
            await Promise.all([loadProducts(), loadUsers()]);
            updateStats();
        }

        // Update statistics
        function updateStats() {
            document.getElementById('totalProducts').textContent = products.length;
            document.getElementById('totalStock').textContent = products.reduce((sum, p) => sum + (p.quantity || 0), 0);
            document.getElementById('lowStock').textContent = products.filter(p => (p.quantity || 0) < 10).length;
            document.getElementById('totalUsers').textContent = users.length;
        }

        // Handle add product
        async function handleAddProduct(e) {
            e.preventDefault();
            const productData = {
                name: document.getElementById('productName').value,
                category: document.getElementById('productCategory').value,
                price: parseFloat(document.getElementById('productPrice').value),
                quantity: parseInt(document.getElementById('productQuantity').value) || 0,
                description: document.getElementById('productDescription').value
            };

            try {
                const response = await axios.post(`${API_BASE}/products`, productData);
                showToast('Success!', 'Product added successfully', 'success');
                document.getElementById('productForm').reset();
                await loadProducts();
                updateStats();
            } catch (error) {
                document.getElementById('productResult').innerHTML =
                    `<div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        ❌ ${error.response?.data?.error || error.message}
                    </div>`;
            }
        }

        // Load products
        async function loadProducts() {
            try {
                const response = await axios.get(`${API_BASE}/products`);
                products = response.data;

                const productsList = document.getElementById('productsList');

                if (products.length === 0) {
                    productsList.innerHTML =
                        '<div class="text-center py-12"><div class="text-gray-400 mb-4"><i data-lucide="package" class="w-16 h-16 mx-auto"></i></div><p class="text-gray-500">No products found. Add some products to get started!</p></div>';
                    lucide.createIcons();
                    return;
                }

                productsList.innerHTML = products.map(product =>
                    `<div class="bg-gradient-to-r from-white to-gray-50 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300 hover-scale">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="package" class="w-6 h-6 text-purple-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-lg text-gray-900">${product.name}</h4>
                                        <p class="text-sm text-gray-500">${product.category || 'Uncategorized'}</p>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-3">${product.description || 'No description available'}</p>
                                <div class="flex items-center space-x-4 text-sm">
                                    <span class="flex items-center space-x-1">
                                        <i data-lucide="tag" class="w-4 h-4 text-gray-400"></i>
                                        <span class="text-gray-600">Supplier: ${product.supplier_name || 'N/A'}</span>
                                    </span>
                                </div>
                            </div>
                            <div class="text-right ml-6">
                                <p class="text-2xl font-bold text-green-600 mb-2">$${product.price}</p>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                        product.quantity < 10
                                            ? 'bg-red-100 text-red-800'
                                            : product.quantity < 50
                                                ? 'bg-yellow-100 text-yellow-800'
                                                : 'bg-green-100 text-green-800'
                                    }">
                                        <i data-lucide="package" class="w-4 h-4 mr-1"></i>
                                        ${product.quantity} in stock
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>`
                ).join('');

                lucide.createIcons();
            } catch (error) {
                document.getElementById('productsList').innerHTML =
                    `<div class="p-6 bg-red-50 border border-red-200 rounded-xl">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="alert-circle" class="w-6 h-6 text-red-600"></i>
                            <div>
                                <h3 class="font-semibold text-red-800">Failed to load products</h3>
                                <p class="text-red-600">${error.response?.data?.error || error.message}</p>
                            </div>
                        </div>
                    </div>`;
                lucide.createIcons();
            }
        }

        // Load users (admin only)
        async function loadUsers() {
            if (currentUser?.role !== 'admin') {
                return;
            }

            try {
                const response = await axios.get(`${API_BASE}/users`);
                users = response.data;

                const usersList = document.getElementById('usersList');

                usersList.innerHTML = users.map(user =>
                    `<div class="bg-gradient-to-r from-white to-gray-50 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300 hover-scale">
                        <div class="flex justify-between items-center">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 ${user.role === 'admin' ? 'bg-red-100' : 'bg-blue-100'} rounded-lg flex items-center justify-center">
                                    <i data-lucide="${user.role === 'admin' ? 'shield' : 'user'}" class="w-6 h-6 ${user.role === 'admin' ? 'text-red-600' : 'text-blue-600'}"></i>
                                </div>
                                <div>
                                    <h4 class="font-bold text-lg text-gray-900">${user.username}</h4>
                                    <p class="text-sm text-gray-500">ID: ${user.id}</p>
                                    <p class="text-xs text-gray-400">Joined: ${new Date(user.created_at).toLocaleDateString()}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                    user.role === 'admin'
                                        ? 'bg-red-100 text-red-800'
                                        : 'bg-blue-100 text-blue-800'
                                }">
                                    <i data-lucide="${user.role === 'admin' ? 'crown' : 'user'}" class="w-4 h-4 mr-1"></i>
                                    ${user.role}
                                </span>
                            </div>
                        </div>
                    </div>`
                ).join('');

                lucide.createIcons();
            } catch (error) {
                document.getElementById('usersList').innerHTML =
                    `<div class="p-6 bg-red-50 border border-red-200 rounded-xl">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="alert-circle" class="w-6 h-6 text-red-600"></i>
                            <div>
                                <h3 class="font-semibold text-red-800">Failed to load users</h3>
                                <p class="text-red-600">${error.response?.data?.error || error.message}</p>
                            </div>
                        </div>
                    </div>`;
                lucide.createIcons();
            }
        }

        // Logout function
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            delete axios.defaults.headers.common['Authorization'];
            authToken = null;
            currentUser = null;

            // Reset UI
            document.getElementById('welcomeSection').style.display = 'block';
            document.getElementById('dashboardSection').classList.add('hidden');
            document.getElementById('navUser').classList.add('hidden');
            document.getElementById('navUser').classList.remove('flex');

            // Hide all sections
            ['addProductSection', 'productsViewSection', 'userManagementSection'].forEach(id => {
                document.getElementById(id).classList.add('hidden');
            });

            showToast('Goodbye!', 'Logged out successfully', 'info');
        }

        // Toast notification system
        function showToast(title, message, type = 'success') {
            const toast = document.getElementById('toast');
            const toastIcon = document.getElementById('toastIcon');
            const toastTitle = document.getElementById('toastTitle');
            const toastMessage = document.getElementById('toastMessage');

            // Set content
            toastTitle.textContent = title;
            toastMessage.textContent = message;

            // Set icon and colors based on type
            const configs = {
                success: { icon: 'check', bgColor: 'bg-green-100', textColor: 'text-green-600' },
                error: { icon: 'x', bgColor: 'bg-red-100', textColor: 'text-red-600' },
                info: { icon: 'info', bgColor: 'bg-blue-100', textColor: 'text-blue-600' },
                warning: { icon: 'alert-triangle', bgColor: 'bg-yellow-100', textColor: 'text-yellow-600' }
            };

            const config = configs[type] || configs.success;
            toastIcon.className = `w-8 h-8 rounded-full flex items-center justify-center ${config.bgColor}`;
            toastIcon.innerHTML = `<i data-lucide="${config.icon}" class="w-5 h-5 ${config.textColor}"></i>`;

            // Show toast
            toast.classList.remove('hidden');
            lucide.createIcons();

            // Auto hide after 3 seconds
            setTimeout(() => {
                toast.classList.add('hidden');
            }, 3000);
        }

        // Initialize the app when page loads
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                lucide.createIcons();
            });
        } else {
            lucide.createIcons();
        }
    </script>
</body>
</html>
