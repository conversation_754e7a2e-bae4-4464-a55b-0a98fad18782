// StockFlow Pro - Main Application JavaScript
class StockFlowApp {
    constructor() {
        this.API_BASE = 'http://localhost:5000/api';
        this.currentUser = null;
        this.authToken = null;
        this.currentSection = 'dashboard';
        this.sidebarOpen = false;
        
        this.init();
    }

    async init() {
        try {
            console.log('Initializing StockFlow Pro...');

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
                console.log('Lucide icons initialized');
            } else {
                console.warn('Lucide not loaded');
            }

            // Setup axios defaults
            this.setupAxios();
            console.log('Axios configured');

            // Check for existing session
            this.checkExistingSession();
            console.log('Session checked');

            // Setup event listeners
            this.setupEventListeners();
            console.log('Event listeners setup');
        } catch (error) {
            console.error('Initialization error:', error);
        }
        
        // Hide loading screen
        setTimeout(() => {
            try {
                document.getElementById('loadingScreen').classList.add('hidden');
                if (this.currentUser) {
                    this.showMainApp();
                } else {
                    this.showLoginScreen();
                }
            } catch (error) {
                console.error('Error hiding loading screen:', error);
                // Fallback - force show login screen
                document.getElementById('loadingScreen').style.display = 'none';
                document.getElementById('loginScreen').classList.remove('hidden');
            }
        }, 800); // Reduced from 1500ms to 800ms
    }

    setupAxios() {
        const token = localStorage.getItem('stockflow_token');
        if (token) {
            axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            this.authToken = token;
        }

        // Response interceptor for token expiration
        axios.interceptors.response.use(
            response => response,
            error => {
                if (error.response?.status === 401) {
                    this.logout();
                }
                return Promise.reject(error);
            }
        );
    }

    checkExistingSession() {
        const token = localStorage.getItem('stockflow_token');
        const user = localStorage.getItem('stockflow_user');
        
        if (token && user) {
            this.authToken = token;
            this.currentUser = JSON.parse(user);
        }
    }

    setupEventListeners() {
        // Login form
        document.getElementById('loginForm').addEventListener('submit', (e) => this.handleLogin(e));
        
        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', () => this.toggleSidebar());
        
        // Register button
        document.getElementById('showRegisterBtn').addEventListener('click', () => this.showRegisterModal());
        
        // Close sidebar when clicking outside
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (this.sidebarOpen && !sidebar.contains(e.target) && !toggle.contains(e.target)) {
                this.closeSidebar();
            }
        });
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;
        
        try {
            const response = await axios.post(`${this.API_BASE}/auth/login`, {
                username, password
            });
            
            this.authToken = response.data.token;
            this.currentUser = response.data.user;
            
            // Store in localStorage
            localStorage.setItem('stockflow_token', this.authToken);
            localStorage.setItem('stockflow_user', JSON.stringify(this.currentUser));
            
            // Set axios header
            axios.defaults.headers.common['Authorization'] = `Bearer ${this.authToken}`;
            
            this.showToast('Welcome!', `Logged in as ${this.currentUser.username}`, 'success');
            this.showMainApp();
            
        } catch (error) {
            this.showToast('Login Failed', error.response?.data?.error || 'Invalid credentials', 'error');
        }
    }

    quickLogin(username, password) {
        document.getElementById('loginUsername').value = username;
        document.getElementById('loginPassword').value = password;
        document.getElementById('loginForm').dispatchEvent(new Event('submit'));
    }

    showLoginScreen() {
        document.getElementById('loginScreen').classList.remove('hidden');
        document.getElementById('mainApp').classList.add('hidden');
    }

    showMainApp() {
        document.getElementById('loginScreen').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
        
        // Update user info
        document.getElementById('userDisplayName').textContent = this.currentUser.username;
        document.getElementById('userRole').textContent = this.currentUser.role.toUpperCase();
        
        // Show admin-only navigation
        if (this.currentUser.role === 'admin') {
            document.getElementById('adminOnlyNav').classList.remove('hidden');
        }
        
        // Load dashboard
        this.showSection('dashboard');
        
        // Open sidebar by default on desktop
        if (window.innerWidth >= 1024) {
            this.openSidebar();
        }
    }

    toggleSidebar() {
        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    openSidebar() {
        document.getElementById('sidebar').classList.remove('sidebar-inactive');
        document.getElementById('sidebar').classList.add('sidebar-active');
        document.getElementById('mainContent').classList.add('ml-64');
        this.sidebarOpen = true;
    }

    closeSidebar() {
        document.getElementById('sidebar').classList.remove('sidebar-active');
        document.getElementById('sidebar').classList.add('sidebar-inactive');
        document.getElementById('mainContent').classList.remove('ml-64');
        this.sidebarOpen = false;
    }

    async showSection(section) {
        this.currentSection = section;
        
        // Update page title
        const titles = {
            dashboard: 'Dashboard',
            products: 'Product Management',
            sales: 'Sales & Billing',
            customers: 'Customer Management',
            suppliers: 'Supplier Management',
            reports: 'Reports & Analytics',
            users: 'User Management',
            settings: 'System Settings'
        };
        
        document.getElementById('pageTitle').textContent = titles[section] || 'Dashboard';
        
        // Update active nav item
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('bg-purple-100', 'text-purple-700');
            item.classList.add('hover:bg-gray-100');
        });
        
        // Load section content
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = '<div class="flex items-center justify-center h-64"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div></div>';
        
        try {
            switch (section) {
                case 'dashboard':
                    await this.loadDashboard();
                    break;
                case 'products':
                    await this.loadProducts();
                    break;
                case 'sales':
                    await this.loadSales();
                    break;
                case 'customers':
                    await this.loadCustomers();
                    break;
                case 'suppliers':
                    await this.loadSuppliers();
                    break;
                case 'reports':
                    await this.loadReports();
                    break;
                case 'users':
                    if (this.currentUser.role === 'admin') {
                        await this.loadUsers();
                    } else {
                        this.showAccessDenied();
                    }
                    break;
                case 'settings':
                    if (this.currentUser.role === 'admin') {
                        await this.loadSettings();
                    } else {
                        this.showAccessDenied();
                    }
                    break;
                default:
                    await this.loadDashboard();
            }
        } catch (error) {
            console.error('Error loading section:', error);
            this.showToast('Error', 'Failed to load section content', 'error');
        }
        
        // Close sidebar on mobile after navigation
        if (window.innerWidth < 1024) {
            this.closeSidebar();
        }
    }

    showAccessDenied() {
        document.getElementById('contentArea').innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="shield-x" class="w-8 h-8 text-red-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Access Denied</h3>
                <p class="text-gray-600">You don't have permission to access this section.</p>
            </div>
        `;
        lucide.createIcons();
    }

    showToast(title, message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        const toastId = 'toast-' + Date.now();
        
        const colors = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };
        
        const icons = {
            success: 'check',
            error: 'x',
            warning: 'alert-triangle',
            info: 'info'
        };
        
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = 'bg-white rounded-lg shadow-lg p-4 max-w-sm transform transition-all duration-300 translate-x-full';
        toast.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 ${colors[type]} rounded-full flex items-center justify-center">
                    <i data-lucide="${icons[type]}" class="w-4 h-4 text-white"></i>
                </div>
                <div class="flex-1">
                    <p class="font-semibold text-gray-900">${title}</p>
                    <p class="text-sm text-gray-600">${message}</p>
                </div>
                <button onclick="app.removeToast('${toastId}')" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        lucide.createIcons();
        
        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            this.removeToast(toastId);
        }, 5000);
    }

    removeToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    }

    async loadDashboard() {
        try {
            const response = await axios.get(`${this.API_BASE}/dashboard/stats`);
            const stats = response.data;

            document.getElementById('contentArea').innerHTML = `
                <div class="space-y-6">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Products</p>
                                    <p class="text-3xl font-bold text-gray-900">${stats.totalProducts}</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="package" class="w-6 h-6 text-blue-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Stock</p>
                                    <p class="text-3xl font-bold text-gray-900">${stats.totalStock}</p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-green-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Low Stock Items</p>
                                    <p class="text-3xl font-bold text-red-600">${stats.lowStockCount}</p>
                                </div>
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                                    <p class="text-3xl font-bold text-purple-600">$${stats.totalRevenue.toFixed(2)}</p>
                                </div>
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-purple-600"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <button onclick="showSection('products')" class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                <i data-lucide="plus" class="w-8 h-8 text-blue-600 mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">Add Product</span>
                            </button>
                            <button onclick="showSection('sales')" class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                <i data-lucide="shopping-cart" class="w-8 h-8 text-green-600 mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">New Sale</span>
                            </button>
                            <button onclick="showSection('customers')" class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                <i data-lucide="user-plus" class="w-8 h-8 text-purple-600 mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">Add Customer</span>
                            </button>
                            <button onclick="showSection('reports')" class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                <i data-lucide="bar-chart" class="w-8 h-8 text-orange-600 mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">View Reports</span>
                            </button>
                        </div>
                    </div>

                    <!-- Alerts and Recent Activity -->
                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- Low Stock Alerts -->
                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Low Stock Alerts</h3>
                            <div class="space-y-3">
                                ${stats.lowStockItems.length > 0 ? stats.lowStockItems.map(item => `
                                    <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                        <div>
                                            <p class="font-medium text-gray-900">${item.name}</p>
                                            <p class="text-sm text-gray-600">Only ${item.quantity} left</p>
                                        </div>
                                        <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">
                                            Low Stock
                                        </span>
                                    </div>
                                `).join('') : '<p class="text-gray-500 text-center py-4">No low stock items</p>'}
                            </div>
                        </div>

                        <!-- Expiring Soon -->
                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Expiring Soon</h3>
                            <div class="space-y-3">
                                ${stats.expiringSoonItems.length > 0 ? stats.expiringSoonItems.map(item => `
                                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                                        <div>
                                            <p class="font-medium text-gray-900">${item.name}</p>
                                            <p class="text-sm text-gray-600">Expires: ${new Date(item.expiry_date).toLocaleDateString()}</p>
                                        </div>
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                                            Expiring
                                        </span>
                                    </div>
                                `).join('') : '<p class="text-gray-500 text-center py-4">No items expiring soon</p>'}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            lucide.createIcons();
        } catch (error) {
            console.error('Dashboard error:', error);
            this.showToast('Error', 'Failed to load dashboard data', 'error');
        }
    }

    async loadProducts() {
        document.getElementById('contentArea').innerHTML = `
            <div class="space-y-6">
                <!-- Header with Search and Add Button -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div class="flex-1 max-w-md">
                        <div class="relative">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"></i>
                            <input type="text" id="productSearch" placeholder="Search products..."
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>
                    </div>
                    <button onclick="app.showAddProductModal()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2">
                        <i data-lucide="plus" class="w-5 h-5"></i>
                        <span>Add Product</span>
                    </button>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <select id="categoryFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <option value="">All Categories</option>
                        </select>
                        <select id="supplierFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <option value="">All Suppliers</option>
                        </select>
                        <select id="stockFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <option value="">All Stock Levels</option>
                            <option value="low">Low Stock</option>
                            <option value="normal">Normal Stock</option>
                        </select>
                        <button onclick="app.searchProducts()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Apply Filters
                        </button>
                    </div>
                </div>

                <!-- Products Grid -->
                <div id="productsGrid" class="grid gap-6">
                    <div class="flex items-center justify-center h-32">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                    </div>
                </div>
            </div>
        `;

        lucide.createIcons();
        await this.loadProductsData();
        await this.loadFiltersData();
        this.setupProductSearch();
    }

    logout() {
        localStorage.removeItem('stockflow_token');
        localStorage.removeItem('stockflow_user');
        delete axios.defaults.headers.common['Authorization'];

        this.currentUser = null;
        this.authToken = null;

        this.showToast('Goodbye!', 'Logged out successfully', 'info');

        setTimeout(() => {
            location.reload();
        }, 1000);
    }
}

    async loadProductsData() {
        try {
            const response = await axios.get(`${this.API_BASE}/products`);
            const products = response.data;

            const productsGrid = document.getElementById('productsGrid');

            if (products.length === 0) {
                productsGrid.innerHTML = `
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="package" class="w-8 h-8 text-gray-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
                        <p class="text-gray-600 mb-4">Get started by adding your first product</p>
                        <button onclick="app.showAddProductModal()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            Add Product
                        </button>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            productsGrid.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    ${products.map(product => `
                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-1">${product.name}</h3>
                                    <p class="text-sm text-gray-600 mb-2">${product.category || 'Uncategorized'}</p>
                                    <p class="text-gray-700 text-sm">${product.description || 'No description'}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-2xl font-bold text-green-600">$${product.price}</p>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                        product.quantity < 10
                                            ? 'bg-red-100 text-red-800'
                                            : product.quantity < 50
                                                ? 'bg-yellow-100 text-yellow-800'
                                                : 'bg-green-100 text-green-800'
                                    }">
                                        ${product.quantity} in stock
                                    </span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between text-sm text-gray-600 mb-4">
                                <span>Supplier: ${product.supplier_name || 'N/A'}</span>
                                ${product.barcode ? `<span>Barcode: ${product.barcode}</span>` : ''}
                            </div>

                            <div class="flex space-x-2">
                                <button onclick="app.editProduct(${product.id})" class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                    Edit
                                </button>
                                <button onclick="app.adjustStock(${product.id})" class="flex-1 bg-green-600 text-white py-2 px-3 rounded-lg hover:bg-green-700 transition-colors text-sm">
                                    Adjust Stock
                                </button>
                                ${this.currentUser.role === 'admin' ? `
                                    <button onclick="app.deleteProduct(${product.id})" class="bg-red-600 text-white py-2 px-3 rounded-lg hover:bg-red-700 transition-colors text-sm">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            lucide.createIcons();
        } catch (error) {
            console.error('Load products error:', error);
            this.showToast('Error', 'Failed to load products', 'error');
        }
    }

    async loadFiltersData() {
        try {
            const [categoriesResponse, suppliersResponse] = await Promise.all([
                axios.get(`${this.API_BASE}/categories`),
                axios.get(`${this.API_BASE}/suppliers`)
            ]);

            const categories = categoriesResponse.data;
            const suppliers = suppliersResponse.data;

            // Populate category filter
            const categoryFilter = document.getElementById('categoryFilter');
            categoryFilter.innerHTML = '<option value="">All Categories</option>' +
                categories.map(cat => `<option value="${cat.name}">${cat.name}</option>`).join('');

            // Populate supplier filter
            const supplierFilter = document.getElementById('supplierFilter');
            supplierFilter.innerHTML = '<option value="">All Suppliers</option>' +
                suppliers.map(sup => `<option value="${sup.id}">${sup.name}</option>`).join('');

        } catch (error) {
            console.error('Load filters error:', error);
        }
    }

    setupProductSearch() {
        const searchInput = document.getElementById('productSearch');
        let searchTimeout;

        searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.searchProducts();
            }, 300);
        });
    }

    async searchProducts() {
        const search = document.getElementById('productSearch').value;
        const category = document.getElementById('categoryFilter').value;
        const supplier_id = document.getElementById('supplierFilter').value;
        const stockFilter = document.getElementById('stockFilter').value;

        const params = new URLSearchParams();
        if (search) params.append('q', search);
        if (category) params.append('category', category);
        if (supplier_id) params.append('supplier_id', supplier_id);
        if (stockFilter === 'low') params.append('low_stock', 'true');

        try {
            const response = await axios.get(`${this.API_BASE}/products/search?${params}`);
            const products = response.data;

            // Update products grid with search results
            this.displayProducts(products);
        } catch (error) {
            console.error('Search products error:', error);
            this.showToast('Error', 'Failed to search products', 'error');
        }
    }

    displayProducts(products) {
        const productsGrid = document.getElementById('productsGrid');

        if (products.length === 0) {
            productsGrid.innerHTML = `
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="search" class="w-8 h-8 text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
                    <p class="text-gray-600">Try adjusting your search criteria</p>
                </div>
            `;
            lucide.createIcons();
            return;
        }

        // Use the same product display logic as loadProductsData
        productsGrid.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                ${products.map(product => `
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">${product.name}</h3>
                                <p class="text-sm text-gray-600 mb-2">${product.category || 'Uncategorized'}</p>
                                <p class="text-gray-700 text-sm">${product.description || 'No description'}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-green-600">$${product.price}</p>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                    product.quantity < 10
                                        ? 'bg-red-100 text-red-800'
                                        : product.quantity < 50
                                            ? 'bg-yellow-100 text-yellow-800'
                                            : 'bg-green-100 text-green-800'
                                }">
                                    ${product.quantity} in stock
                                </span>
                            </div>
                        </div>

                        <div class="flex items-center justify-between text-sm text-gray-600 mb-4">
                            <span>Supplier: ${product.supplier_name || 'N/A'}</span>
                            ${product.barcode ? `<span>Barcode: ${product.barcode}</span>` : ''}
                        </div>

                        <div class="flex space-x-2">
                            <button onclick="app.editProduct(${product.id})" class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                Edit
                            </button>
                            <button onclick="app.adjustStock(${product.id})" class="flex-1 bg-green-600 text-white py-2 px-3 rounded-lg hover:bg-green-700 transition-colors text-sm">
                                Adjust Stock
                            </button>
                            ${this.currentUser.role === 'admin' ? `
                                <button onclick="app.deleteProduct(${product.id})" class="bg-red-600 text-white py-2 px-3 rounded-lg hover:bg-red-700 transition-colors text-sm">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        lucide.createIcons();
    }

    // Placeholder methods for other sections
    async loadSales() {
        document.getElementById('contentArea').innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="shopping-cart" class="w-8 h-8 text-blue-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Sales & Billing</h3>
                <p class="text-gray-600">Coming soon - Advanced POS system with invoice generation</p>
            </div>
        `;
        lucide.createIcons();
    }

    async loadCustomers() {
        document.getElementById('contentArea').innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="users" class="w-8 h-8 text-green-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Customer Management</h3>
                <p class="text-gray-600">Coming soon - Complete customer database with purchase history</p>
            </div>
        `;
        lucide.createIcons();
    }

    async loadSuppliers() {
        document.getElementById('contentArea').innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="truck" class="w-8 h-8 text-purple-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Supplier Management</h3>
                <p class="text-gray-600">Coming soon - Supplier database with purchase orders</p>
            </div>
        `;
        lucide.createIcons();
    }

    async loadReports() {
        document.getElementById('contentArea').innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="bar-chart" class="w-8 h-8 text-orange-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Reports & Analytics</h3>
                <p class="text-gray-600">Coming soon - Comprehensive reporting with charts and exports</p>
            </div>
        `;
        lucide.createIcons();
    }

    async loadUsers() {
        document.getElementById('contentArea').innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="user-cog" class="w-8 h-8 text-red-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">User Management</h3>
                <p class="text-gray-600">Coming soon - User roles and permissions management</p>
            </div>
        `;
        lucide.createIcons();
    }

    async loadSettings() {
        document.getElementById('contentArea').innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="settings" class="w-8 h-8 text-gray-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">System Settings</h3>
                <p class="text-gray-600">Coming soon - Company settings and system configuration</p>
            </div>
        `;
        lucide.createIcons();
    }
}

// Initialize the application when DOM is ready
let app;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, initializing app...');
        app = new StockFlowApp();
    });
} else {
    console.log('DOM already loaded, initializing app...');
    app = new StockFlowApp();
}

// Global functions for HTML onclick events
function quickLogin(username, password) {
    if (app) {
        app.quickLogin(username, password);
    } else {
        console.error('App not initialized yet');
    }
}

function showSection(section) {
    if (app) {
        app.showSection(section);
    } else {
        console.error('App not initialized yet');
    }
}

function logout() {
    if (app) {
        app.logout();
    } else {
        console.error('App not initialized yet');
    }
}
