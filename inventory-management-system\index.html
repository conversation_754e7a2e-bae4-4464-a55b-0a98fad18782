<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StockFlow Pro - Complete Inventory Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .glass { backdrop-filter: blur(16px); background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); }
        .animate-fade-in { animation: fadeIn 0.6s ease-out; }
        .animate-slide-up { animation: slideUp 0.5s ease-out; }
        .animate-bounce-in { animation: bounceIn 0.8s ease-out; }
        .hover-scale { transition: transform 0.3s ease; }
        .hover-scale:hover { transform: scale(1.02); }
        .card-shadow { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
        .sidebar-active { transform: translateX(0); }
        .sidebar-inactive { transform: translateX(-100%); }
        
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        @keyframes slideUp { from { transform: translateY(30px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
        @keyframes bounceIn { 
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .floating { animation: floating 3s ease-in-out infinite; }
        @keyframes floating { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
        
        .pulse-ring { animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite; }
        @keyframes pulse-ring { 0% { transform: scale(0.33); } 40%, 50% { opacity: 0; } 100% { opacity: 0; transform: scale(1.33); } }
        
        .print-hidden { display: none !important; }
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body { background: white !important; }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Loading Screen -->
    <div id="loadingScreen" class="fixed inset-0 gradient-bg flex items-center justify-center z-50">
        <div class="text-center text-white">
            <div class="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h2 class="text-2xl font-bold">Loading StockFlow Pro...</h2>
            <p class="opacity-80">Initializing your inventory management system</p>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="loginScreen" class="fixed inset-0 gradient-bg flex items-center justify-center z-40 hidden">
        <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 animate-slide-up">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="package" class="w-8 h-8 text-purple-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-900">StockFlow Pro</h1>
                <p class="text-gray-600">Complete Inventory Management System</p>
            </div>

            <!-- Quick Login Options -->
            <div class="grid grid-cols-2 gap-3 mb-6">
                <button onclick="quickLogin('admin', 'admin123')" class="bg-purple-100 text-purple-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-purple-200 transition-colors">
                    <i data-lucide="shield" class="w-4 h-4 inline mr-1"></i>
                    Admin Login
                </button>
                <button onclick="quickLogin('employee', 'emp123')" class="bg-blue-100 text-blue-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors">
                    <i data-lucide="user" class="w-4 h-4 inline mr-1"></i>
                    Employee Login
                </button>
            </div>

            <form id="loginForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                    <input type="text" id="loginUsername" required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="loginPassword" required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                </div>
                <button type="submit" class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition duration-200 font-semibold">
                    Sign In
                </button>
            </form>
            
            <div class="mt-6 text-center">
                <button id="showRegisterBtn" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                    Create New Account
                </button>
            </div>
            
            <div id="loginResult" class="mt-4"></div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="hidden">
        <!-- Mobile Overlay -->
        <div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden lg:hidden"></div>

        <!-- Sidebar -->
        <div id="sidebar" class="fixed left-0 top-0 h-full w-64 bg-white shadow-lg z-30 transform transition-transform duration-300 -translate-x-full lg:translate-x-0">
            <div class="p-4 lg:p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 lg:w-10 lg:h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="package" class="w-4 h-4 lg:w-6 lg:h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-lg lg:text-xl font-bold text-gray-900">StockFlow</h1>
                            <p class="text-xs lg:text-sm text-gray-500">Pro Edition</p>
                        </div>
                    </div>
                    <button id="closeSidebar" class="lg:hidden p-2 rounded-lg hover:bg-gray-100">
                        <i data-lucide="x" class="w-5 h-5 text-gray-600"></i>
                    </button>
                </div>
            </div>
            
            <nav class="p-4">
                <div class="space-y-2">
                    <button onclick="showSection('dashboard')" class="nav-item w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-lucide="home" class="w-5 h-5 text-gray-500"></i>
                        <span class="font-medium text-gray-700">Dashboard</span>
                    </button>
                    <button onclick="showSection('products')" class="nav-item w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-lucide="package" class="w-5 h-5 text-gray-500"></i>
                        <span class="font-medium text-gray-700">Products</span>
                    </button>
                    <button onclick="showSection('sales')" class="nav-item w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-lucide="shopping-cart" class="w-5 h-5 text-gray-500"></i>
                        <span class="font-medium text-gray-700">Sales & Billing</span>
                    </button>
                    <button onclick="showSection('customers')" class="nav-item w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-lucide="users" class="w-5 h-5 text-gray-500"></i>
                        <span class="font-medium text-gray-700">Customers</span>
                    </button>
                    <button onclick="showSection('suppliers')" class="nav-item w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-lucide="truck" class="w-5 h-5 text-gray-500"></i>
                        <span class="font-medium text-gray-700">Suppliers</span>
                    </button>
                    <button onclick="showSection('reports')" class="nav-item w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-lucide="bar-chart" class="w-5 h-5 text-gray-500"></i>
                        <span class="font-medium text-gray-700">Reports</span>
                    </button>
                    <div id="adminOnlyNav" class="hidden">
                        <button onclick="showSection('users')" class="nav-item w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg hover:bg-gray-100 transition-colors">
                            <i data-lucide="user-cog" class="w-5 h-5 text-gray-500"></i>
                            <span class="font-medium text-gray-700">User Management</span>
                        </button>
                        <button onclick="showSection('settings')" class="nav-item w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg hover:bg-gray-100 transition-colors">
                            <i data-lucide="settings" class="w-5 h-5 text-gray-500"></i>
                            <span class="font-medium text-gray-700">Settings</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="lg:ml-64 transition-all duration-300" id="mainContent">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
                <div class="flex items-center justify-between px-4 lg:px-6 py-3 lg:py-4">
                    <div class="flex items-center space-x-2 lg:space-x-4">
                        <button id="sidebarToggle" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                            <i data-lucide="menu" class="w-6 h-6 text-gray-600"></i>
                        </button>
                        <h2 id="pageTitle" class="text-lg lg:text-2xl font-bold text-gray-900">Dashboard</h2>
                    </div>

                    <div class="flex items-center space-x-2 lg:space-x-4">
                        <!-- Notification Button -->
                        <div class="relative">
                            <button id="notificationBtn" class="p-2 rounded-lg hover:bg-gray-100 transition-colors relative">
                                <i data-lucide="bell" class="w-5 h-5 lg:w-6 lg:h-6 text-gray-600"></i>
                                <span id="notificationBadge" class="absolute -top-1 -right-1 w-4 h-4 lg:w-5 lg:h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center hidden">0</span>
                            </button>

                            <!-- Notification Dropdown -->
                            <div id="notificationDropdown" class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 hidden z-50">
                                <div class="p-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                                </div>
                                <div id="notificationList" class="max-h-64 overflow-y-auto">
                                    <!-- Notifications will be loaded here -->
                                </div>
                                <div class="p-3 border-t border-gray-200">
                                    <button class="text-sm text-purple-600 hover:text-purple-700 font-medium">Mark all as read</button>
                                </div>
                            </div>
                        </div>

                        <!-- User Profile -->
                        <div class="relative">
                            <button id="profileBtn" class="flex items-center space-x-2 lg:space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors">
                                <div class="hidden sm:block text-right">
                                    <p id="userDisplayName" class="text-sm font-medium text-gray-900"></p>
                                    <p id="userRole" class="text-xs text-gray-500"></p>
                                </div>
                                <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                                    <i data-lucide="user" class="w-4 h-4 text-white"></i>
                                </div>
                                <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400 hidden lg:block"></i>
                            </button>

                            <!-- Profile Dropdown -->
                            <div id="profileDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden z-50">
                                <div class="p-3 border-b border-gray-200">
                                    <p class="font-medium text-gray-900" id="profileName"></p>
                                    <p class="text-sm text-gray-500" id="profileRole"></p>
                                </div>
                                <div class="py-2">
                                    <button onclick="showProfile()" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                        <span>My Profile</span>
                                    </button>
                                    <button onclick="showSettings()" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        <span>Settings</span>
                                    </button>
                                    <hr class="my-2">
                                    <button onclick="logout()" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                                        <i data-lucide="log-out" class="w-4 h-4"></i>
                                        <span>Logout</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <main class="p-4 lg:p-6">
                <div id="contentArea">
                    <!-- Content will be loaded here -->
                </div>
            </main>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <script>
        console.log('Starting StockFlow Pro initialization...');

        // Simple initialization without complex class structure
        let currentUser = null;
        let authToken = null;
        const API_BASE = 'http://localhost:5000/api';

        // Check for existing session
        function checkSession() {
            const token = localStorage.getItem('stockflow_token');
            const user = localStorage.getItem('stockflow_user');

            if (token && user) {
                authToken = token;
                currentUser = JSON.parse(user);
                axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            }
        }

        // Initialize the app
        function initApp() {
            console.log('Initializing app...');

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
                console.log('Lucide icons initialized');
            }

            // Setup axios
            if (typeof axios !== 'undefined') {
                console.log('Axios available');
                checkSession();
            }

            // Setup event listeners
            setupEventListeners();

            // Hide loading screen and show appropriate screen
            setTimeout(() => {
                document.getElementById('loadingScreen').classList.add('hidden');
                if (currentUser) {
                    showMainApp();
                } else {
                    showLoginScreen();
                }
            }, 1000);
        }

        function setupEventListeners() {
            // Login form
            document.getElementById('loginForm').addEventListener('submit', handleLogin);

            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);
            document.getElementById('closeSidebar').addEventListener('click', closeSidebar);
            document.getElementById('sidebarOverlay').addEventListener('click', closeSidebar);

            // Profile dropdown
            document.getElementById('profileBtn').addEventListener('click', toggleProfileDropdown);

            // Notification dropdown
            document.getElementById('notificationBtn').addEventListener('click', toggleNotificationDropdown);

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('#profileBtn') && !e.target.closest('#profileDropdown')) {
                    document.getElementById('profileDropdown').classList.add('hidden');
                }
                if (!e.target.closest('#notificationBtn') && !e.target.closest('#notificationDropdown')) {
                    document.getElementById('notificationDropdown').classList.add('hidden');
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    // Desktop - show sidebar, hide overlay
                    document.getElementById('sidebar').classList.remove('-translate-x-full');
                    document.getElementById('sidebarOverlay').classList.add('hidden');
                    sidebarOpen = true;
                } else {
                    // Mobile - hide sidebar unless explicitly opened
                    if (!sidebarOpen) {
                        document.getElementById('sidebar').classList.add('-translate-x-full');
                    }
                }
            });
        }

        async function handleLogin(e) {
            e.preventDefault();

            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await axios.post(`${API_BASE}/auth/login`, {
                    username, password
                });

                authToken = response.data.token;
                currentUser = response.data.user;

                localStorage.setItem('stockflow_token', authToken);
                localStorage.setItem('stockflow_user', JSON.stringify(currentUser));
                axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;

                showToast('Welcome!', `Logged in as ${currentUser.username}`, 'success');
                showMainApp();

            } catch (error) {
                showToast('Login Failed', error.response?.data?.error || 'Invalid credentials', 'error');
            }
        }

        function quickLogin(username, password) {
            document.getElementById('loginUsername').value = username;
            document.getElementById('loginPassword').value = password;
            document.getElementById('loginForm').dispatchEvent(new Event('submit'));
        }

        function showLoginScreen() {
            document.getElementById('loginScreen').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
        }

        function showMainApp() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');

            // Update user info
            document.getElementById('userDisplayName').textContent = currentUser.username;
            document.getElementById('userRole').textContent = currentUser.role.toUpperCase();
            document.getElementById('profileName').textContent = currentUser.username;
            document.getElementById('profileRole').textContent = currentUser.role.toUpperCase();

            // Show admin-only navigation
            if (currentUser.role === 'admin') {
                document.getElementById('adminOnlyNav').classList.remove('hidden');
            }

            // Load dashboard
            showSection('dashboard');

            // Load notifications
            loadNotifications();

            // Initialize sidebar state based on screen size
            if (window.innerWidth >= 1024) {
                openSidebar();
            }
        }

        let sidebarOpen = false;

        function toggleSidebar() {
            if (sidebarOpen) {
                closeSidebar();
            } else {
                openSidebar();
            }
        }

        function openSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.remove('-translate-x-full');

            if (window.innerWidth < 1024) {
                // Mobile - show overlay
                overlay.classList.remove('hidden');
            }

            sidebarOpen = true;
        }

        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            if (window.innerWidth < 1024) {
                // Mobile - hide sidebar and overlay
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                sidebarOpen = false;
            }
            // On desktop, keep sidebar open
        }

        function toggleProfileDropdown() {
            const dropdown = document.getElementById('profileDropdown');
            dropdown.classList.toggle('hidden');

            // Close notification dropdown if open
            document.getElementById('notificationDropdown').classList.add('hidden');
        }

        function toggleNotificationDropdown() {
            const dropdown = document.getElementById('notificationDropdown');
            dropdown.classList.toggle('hidden');

            // Close profile dropdown if open
            document.getElementById('profileDropdown').classList.add('hidden');

            // Mark notifications as read
            markNotificationsAsRead();
        }

        function loadNotifications() {
            // Simulate loading notifications
            const notifications = [
                {
                    id: 1,
                    title: 'Low Stock Alert',
                    message: 'Organic Coffee Beans is running low (5 items left)',
                    type: 'warning',
                    time: '2 minutes ago',
                    read: false
                },
                {
                    id: 2,
                    title: 'New Sale',
                    message: 'Sale completed for $299.99',
                    type: 'success',
                    time: '1 hour ago',
                    read: false
                },
                {
                    id: 3,
                    title: 'Product Added',
                    message: 'New product "Wireless Mouse" added to inventory',
                    type: 'info',
                    time: '3 hours ago',
                    read: true
                }
            ];

            const unreadCount = notifications.filter(n => !n.read).length;
            const badge = document.getElementById('notificationBadge');

            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }

            const notificationList = document.getElementById('notificationList');
            notificationList.innerHTML = notifications.map(notification => `
                <div class="p-4 border-b border-gray-100 hover:bg-gray-50 ${notification.read ? 'opacity-60' : ''}">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center ${
                            notification.type === 'warning' ? 'bg-yellow-100' :
                            notification.type === 'success' ? 'bg-green-100' : 'bg-blue-100'
                        }">
                            <i data-lucide="${
                                notification.type === 'warning' ? 'alert-triangle' :
                                notification.type === 'success' ? 'check' : 'info'
                            }" class="w-4 h-4 ${
                                notification.type === 'warning' ? 'text-yellow-600' :
                                notification.type === 'success' ? 'text-green-600' : 'text-blue-600'
                            }"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900 text-sm">${notification.title}</p>
                            <p class="text-gray-600 text-sm">${notification.message}</p>
                            <p class="text-gray-400 text-xs mt-1">${notification.time}</p>
                        </div>
                        ${!notification.read ? '<div class="w-2 h-2 bg-blue-500 rounded-full"></div>' : ''}
                    </div>
                </div>
            `).join('');

            lucide.createIcons();
        }

        function markNotificationsAsRead() {
            // Hide the notification badge
            document.getElementById('notificationBadge').classList.add('hidden');

            // Update notification styles to show as read
            setTimeout(() => {
                const notifications = document.querySelectorAll('#notificationList > div');
                notifications.forEach(notification => {
                    notification.classList.add('opacity-60');
                    const unreadDot = notification.querySelector('.bg-blue-500');
                    if (unreadDot) {
                        unreadDot.remove();
                    }
                });
            }, 100);
        }

        function showProfile() {
            // Close dropdown
            document.getElementById('profileDropdown').classList.add('hidden');

            // Show profile modal or section
            showToast('Profile', 'Profile management coming soon!', 'info');
        }

        function showSettings() {
            // Close dropdown
            document.getElementById('profileDropdown').classList.add('hidden');

            // Navigate to settings
            if (currentUser.role === 'admin') {
                showSection('settings');
            } else {
                showToast('Access Denied', 'Only admins can access settings', 'warning');
            }
        }

        function showSection(section) {
            document.getElementById('pageTitle').textContent = section.charAt(0).toUpperCase() + section.slice(1);

            const contentArea = document.getElementById('contentArea');

            switch (section) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'products':
                    loadProducts();
                    break;
                default:
                    contentArea.innerHTML = `
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="construction" class="w-8 h-8 text-blue-600"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">${section.charAt(0).toUpperCase() + section.slice(1)}</h3>
                            <p class="text-gray-600">This section is under development</p>
                        </div>
                    `;
                    lucide.createIcons();
            }

            // Close sidebar on mobile
            if (window.innerWidth < 1024) {
                closeSidebar();
            }
        }

        async function loadDashboard() {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = `
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Products</p>
                                    <p class="text-3xl font-bold text-gray-900" id="totalProducts">Loading...</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="package" class="w-6 h-6 text-blue-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Stock</p>
                                    <p class="text-3xl font-bold text-gray-900" id="totalStock">Loading...</p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-green-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Low Stock Items</p>
                                    <p class="text-3xl font-bold text-red-600" id="lowStock">Loading...</p>
                                </div>
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                                    <p class="text-3xl font-bold text-purple-600" id="totalRevenue">Loading...</p>
                                </div>
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-purple-600"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <button onclick="showSection('products')" class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                <i data-lucide="plus" class="w-8 h-8 text-blue-600 mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">Add Product</span>
                            </button>
                            <button onclick="showSection('sales')" class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                <i data-lucide="shopping-cart" class="w-8 h-8 text-green-600 mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">New Sale</span>
                            </button>
                            <button onclick="showSection('customers')" class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                <i data-lucide="user-plus" class="w-8 h-8 text-purple-600 mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">Add Customer</span>
                            </button>
                            <button onclick="showSection('reports')" class="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                <i data-lucide="bar-chart" class="w-8 h-8 text-orange-600 mb-2"></i>
                                <span class="text-sm font-medium text-gray-700">View Reports</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            lucide.createIcons();

            // Load dashboard stats
            try {
                const response = await axios.get(`${API_BASE}/dashboard/stats`);
                const stats = response.data;

                document.getElementById('totalProducts').textContent = stats.totalProducts;
                document.getElementById('totalStock').textContent = stats.totalStock;
                document.getElementById('lowStock').textContent = stats.lowStockCount;
                document.getElementById('totalRevenue').textContent = `$${stats.totalRevenue.toFixed(2)}`;
            } catch (error) {
                console.error('Dashboard error:', error);
                document.getElementById('totalProducts').textContent = '0';
                document.getElementById('totalStock').textContent = '0';
                document.getElementById('lowStock').textContent = '0';
                document.getElementById('totalRevenue').textContent = '$0.00';
            }
        }

        function loadProducts() {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = `
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="package" class="w-8 h-8 text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Product Management</h3>
                    <p class="text-gray-600">Advanced product management features coming soon</p>
                </div>
            `;
            lucide.createIcons();
        }

        function showToast(title, message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer');
            const toastId = 'toast-' + Date.now();

            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            };

            const icons = {
                success: 'check',
                error: 'x',
                warning: 'alert-triangle',
                info: 'info'
            };

            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = 'bg-white rounded-lg shadow-lg p-4 max-w-sm transform transition-all duration-300 translate-x-full';
            toast.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 ${colors[type]} rounded-full flex items-center justify-center">
                        <i data-lucide="${icons[type]}" class="w-4 h-4 text-white"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-semibold text-gray-900">${title}</p>
                        <p class="text-sm text-gray-600">${message}</p>
                    </div>
                    <button onclick="removeToast('${toastId}')" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            `;

            toastContainer.appendChild(toast);
            lucide.createIcons();

            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            setTimeout(() => {
                removeToast(toastId);
            }, 5000);
        }

        function removeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }

        function logout() {
            localStorage.removeItem('stockflow_token');
            localStorage.removeItem('stockflow_user');
            delete axios.defaults.headers.common['Authorization'];

            currentUser = null;
            authToken = null;

            showToast('Goodbye!', 'Logged out successfully', 'info');

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initApp);
        } else {
            initApp();
        }
    </script>
</body>
</html>
