{"version": 3, "file": "workbox-core.dev.js", "sources": ["../_version.js", "../_private/logger.js", "../models/messages/messages.js", "../models/messages/messageGenerator.js", "../_private/WorkboxError.js", "../_private/assert.js", "../models/quotaErrorCallbacks.js", "../registerQuotaErrorCallback.js", "../_private/cacheNames.js", "../_private/cacheMatchIgnoreParams.js", "../_private/canConstructReadableStream.js", "../_private/canConstructResponseFromBodyStream.js", "../_private/dontWaitFor.js", "../_private/Deferred.js", "../_private/executeQuotaErrorCallbacks.js", "../_private/getFriendlyURL.js", "../_private/timeout.js", "../_private/resultingClientExists.js", "../_private/waitUntil.js", "../_private.js", "../cacheNames.js", "../copyResponse.js", "../clientsClaim.js", "../setCacheNameDetails.js", "../skipWaiting.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst logger = (process.env.NODE_ENV === 'production'\n    ? null\n    : (() => {\n        // Don't overwrite this value if it's already set.\n        // See https://github.com/GoogleChrome/workbox/pull/2284#issuecomment-560470923\n        if (!('__WB_DISABLE_DEV_LOGS' in globalThis)) {\n            self.__WB_DISABLE_DEV_LOGS = false;\n        }\n        let inGroup = false;\n        const methodToColorMap = {\n            debug: `#7f8c8d`,\n            log: `#2ecc71`,\n            warn: `#f39c12`,\n            error: `#c0392b`,\n            groupCollapsed: `#3498db`,\n            groupEnd: null, // No colored prefix on groupEnd\n        };\n        const print = function (method, args) {\n            if (self.__WB_DISABLE_DEV_LOGS) {\n                return;\n            }\n            if (method === 'groupCollapsed') {\n                // Safari doesn't print all console.groupCollapsed() arguments:\n                // https://bugs.webkit.org/show_bug.cgi?id=182754\n                if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n                    console[method](...args);\n                    return;\n                }\n            }\n            const styles = [\n                `background: ${methodToColorMap[method]}`,\n                `border-radius: 0.5em`,\n                `color: white`,\n                `font-weight: bold`,\n                `padding: 2px 0.5em`,\n            ];\n            // When in a group, the workbox prefix is not displayed.\n            const logPrefix = inGroup ? [] : ['%cworkbox', styles.join(';')];\n            console[method](...logPrefix, ...args);\n            if (method === 'groupCollapsed') {\n                inGroup = true;\n            }\n            if (method === 'groupEnd') {\n                inGroup = false;\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const api = {};\n        const loggerMethods = Object.keys(methodToColorMap);\n        for (const key of loggerMethods) {\n            const method = key;\n            api[method] = (...args) => {\n                print(method, args);\n            };\n        }\n        return api;\n    })());\nexport { logger };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../../_version.js';\nexport const messages = {\n    'invalid-value': ({ paramName, validValueDescription, value }) => {\n        if (!paramName || !validValueDescription) {\n            throw new Error(`Unexpected input to 'invalid-value' error.`);\n        }\n        return (`The '${paramName}' parameter was given a value with an ` +\n            `unexpected value. ${validValueDescription} Received a value of ` +\n            `${JSON.stringify(value)}.`);\n    },\n    'not-an-array': ({ moduleName, className, funcName, paramName }) => {\n        if (!moduleName || !className || !funcName || !paramName) {\n            throw new Error(`Unexpected input to 'not-an-array' error.`);\n        }\n        return (`The parameter '${paramName}' passed into ` +\n            `'${moduleName}.${className}.${funcName}()' must be an array.`);\n    },\n    'incorrect-type': ({ expectedType, paramName, moduleName, className, funcName, }) => {\n        if (!expectedType || !paramName || !moduleName || !funcName) {\n            throw new Error(`Unexpected input to 'incorrect-type' error.`);\n        }\n        const classNameStr = className ? `${className}.` : '';\n        return (`The parameter '${paramName}' passed into ` +\n            `'${moduleName}.${classNameStr}` +\n            `${funcName}()' must be of type ${expectedType}.`);\n    },\n    'incorrect-class': ({ expectedClassName, paramName, moduleName, className, funcName, isReturnValueProblem, }) => {\n        if (!expectedClassName || !moduleName || !funcName) {\n            throw new Error(`Unexpected input to 'incorrect-class' error.`);\n        }\n        const classNameStr = className ? `${className}.` : '';\n        if (isReturnValueProblem) {\n            return (`The return value from ` +\n                `'${moduleName}.${classNameStr}${funcName}()' ` +\n                `must be an instance of class ${expectedClassName}.`);\n        }\n        return (`The parameter '${paramName}' passed into ` +\n            `'${moduleName}.${classNameStr}${funcName}()' ` +\n            `must be an instance of class ${expectedClassName}.`);\n    },\n    'missing-a-method': ({ expectedMethod, paramName, moduleName, className, funcName, }) => {\n        if (!expectedMethod ||\n            !paramName ||\n            !moduleName ||\n            !className ||\n            !funcName) {\n            throw new Error(`Unexpected input to 'missing-a-method' error.`);\n        }\n        return (`${moduleName}.${className}.${funcName}() expected the ` +\n            `'${paramName}' parameter to expose a '${expectedMethod}' method.`);\n    },\n    'add-to-cache-list-unexpected-type': ({ entry }) => {\n        return (`An unexpected entry was passed to ` +\n            `'workbox-precaching.PrecacheController.addToCacheList()' The entry ` +\n            `'${JSON.stringify(entry)}' isn't supported. You must supply an array of ` +\n            `strings with one or more characters, objects with a url property or ` +\n            `Request objects.`);\n    },\n    'add-to-cache-list-conflicting-entries': ({ firstEntry, secondEntry }) => {\n        if (!firstEntry || !secondEntry) {\n            throw new Error(`Unexpected input to ` + `'add-to-cache-list-duplicate-entries' error.`);\n        }\n        return (`Two of the entries passed to ` +\n            `'workbox-precaching.PrecacheController.addToCacheList()' had the URL ` +\n            `${firstEntry} but different revision details. Workbox is ` +\n            `unable to cache and version the asset correctly. Please remove one ` +\n            `of the entries.`);\n    },\n    'plugin-error-request-will-fetch': ({ thrownErrorMessage }) => {\n        if (!thrownErrorMessage) {\n            throw new Error(`Unexpected input to ` + `'plugin-error-request-will-fetch', error.`);\n        }\n        return (`An error was thrown by a plugins 'requestWillFetch()' method. ` +\n            `The thrown error message was: '${thrownErrorMessage}'.`);\n    },\n    'invalid-cache-name': ({ cacheNameId, value }) => {\n        if (!cacheNameId) {\n            throw new Error(`Expected a 'cacheNameId' for error 'invalid-cache-name'`);\n        }\n        return (`You must provide a name containing at least one character for ` +\n            `setCacheDetails({${cacheNameId}: '...'}). Received a value of ` +\n            `'${JSON.stringify(value)}'`);\n    },\n    'unregister-route-but-not-found-with-method': ({ method }) => {\n        if (!method) {\n            throw new Error(`Unexpected input to ` +\n                `'unregister-route-but-not-found-with-method' error.`);\n        }\n        return (`The route you're trying to unregister was not  previously ` +\n            `registered for the method type '${method}'.`);\n    },\n    'unregister-route-route-not-registered': () => {\n        return (`The route you're trying to unregister was not previously ` +\n            `registered.`);\n    },\n    'queue-replay-failed': ({ name }) => {\n        return `Replaying the background sync queue '${name}' failed.`;\n    },\n    'duplicate-queue-name': ({ name }) => {\n        return (`The Queue name '${name}' is already being used. ` +\n            `All instances of backgroundSync.Queue must be given unique names.`);\n    },\n    'expired-test-without-max-age': ({ methodName, paramName }) => {\n        return (`The '${methodName}()' method can only be used when the ` +\n            `'${paramName}' is used in the constructor.`);\n    },\n    'unsupported-route-type': ({ moduleName, className, funcName, paramName }) => {\n        return (`The supplied '${paramName}' parameter was an unsupported type. ` +\n            `Please check the docs for ${moduleName}.${className}.${funcName} for ` +\n            `valid input types.`);\n    },\n    'not-array-of-class': ({ value, expectedClass, moduleName, className, funcName, paramName, }) => {\n        return (`The supplied '${paramName}' parameter must be an array of ` +\n            `'${expectedClass}' objects. Received '${JSON.stringify(value)},'. ` +\n            `Please check the call to ${moduleName}.${className}.${funcName}() ` +\n            `to fix the issue.`);\n    },\n    'max-entries-or-age-required': ({ moduleName, className, funcName }) => {\n        return (`You must define either config.maxEntries or config.maxAgeSeconds` +\n            `in ${moduleName}.${className}.${funcName}`);\n    },\n    'statuses-or-headers-required': ({ moduleName, className, funcName }) => {\n        return (`You must define either config.statuses or config.headers` +\n            `in ${moduleName}.${className}.${funcName}`);\n    },\n    'invalid-string': ({ moduleName, funcName, paramName }) => {\n        if (!paramName || !moduleName || !funcName) {\n            throw new Error(`Unexpected input to 'invalid-string' error.`);\n        }\n        return (`When using strings, the '${paramName}' parameter must start with ` +\n            `'http' (for cross-origin matches) or '/' (for same-origin matches). ` +\n            `Please see the docs for ${moduleName}.${funcName}() for ` +\n            `more info.`);\n    },\n    'channel-name-required': () => {\n        return (`You must provide a channelName to construct a ` +\n            `BroadcastCacheUpdate instance.`);\n    },\n    'invalid-responses-are-same-args': () => {\n        return (`The arguments passed into responsesAreSame() appear to be ` +\n            `invalid. Please ensure valid Responses are used.`);\n    },\n    'expire-custom-caches-only': () => {\n        return (`You must provide a 'cacheName' property when using the ` +\n            `expiration plugin with a runtime caching strategy.`);\n    },\n    'unit-must-be-bytes': ({ normalizedRangeHeader }) => {\n        if (!normalizedRangeHeader) {\n            throw new Error(`Unexpected input to 'unit-must-be-bytes' error.`);\n        }\n        return (`The 'unit' portion of the Range header must be set to 'bytes'. ` +\n            `The Range header provided was \"${normalizedRangeHeader}\"`);\n    },\n    'single-range-only': ({ normalizedRangeHeader }) => {\n        if (!normalizedRangeHeader) {\n            throw new Error(`Unexpected input to 'single-range-only' error.`);\n        }\n        return (`Multiple ranges are not supported. Please use a  single start ` +\n            `value, and optional end value. The Range header provided was ` +\n            `\"${normalizedRangeHeader}\"`);\n    },\n    'invalid-range-values': ({ normalizedRangeHeader }) => {\n        if (!normalizedRangeHeader) {\n            throw new Error(`Unexpected input to 'invalid-range-values' error.`);\n        }\n        return (`The Range header is missing both start and end values. At least ` +\n            `one of those values is needed. The Range header provided was ` +\n            `\"${normalizedRangeHeader}\"`);\n    },\n    'no-range-header': () => {\n        return `No Range header was found in the Request provided.`;\n    },\n    'range-not-satisfiable': ({ size, start, end }) => {\n        return (`The start (${start}) and end (${end}) values in the Range are ` +\n            `not satisfiable by the cached response, which is ${size} bytes.`);\n    },\n    'attempt-to-cache-non-get-request': ({ url, method }) => {\n        return (`Unable to cache '${url}' because it is a '${method}' request and ` +\n            `only 'GET' requests can be cached.`);\n    },\n    'cache-put-with-no-response': ({ url }) => {\n        return (`There was an attempt to cache '${url}' but the response was not ` +\n            `defined.`);\n    },\n    'no-response': ({ url, error }) => {\n        let message = `The strategy could not generate a response for '${url}'.`;\n        if (error) {\n            message += ` The underlying error is ${error}.`;\n        }\n        return message;\n    },\n    'bad-precaching-response': ({ url, status }) => {\n        return (`The precaching request for '${url}' failed` +\n            (status ? ` with an HTTP status of ${status}.` : `.`));\n    },\n    'non-precached-url': ({ url }) => {\n        return (`createHandlerBoundToURL('${url}') was called, but that URL is ` +\n            `not precached. Please pass in a URL that is precached instead.`);\n    },\n    'add-to-cache-list-conflicting-integrities': ({ url }) => {\n        return (`Two of the entries passed to ` +\n            `'workbox-precaching.PrecacheController.addToCacheList()' had the URL ` +\n            `${url} with different integrity values. Please remove one of them.`);\n    },\n    'missing-precache-entry': ({ cacheName, url }) => {\n        return `Unable to find a precached response in ${cacheName} for ${url}.`;\n    },\n    'cross-origin-copy-response': ({ origin }) => {\n        return (`workbox-core.copyResponse() can only be used with same-origin ` +\n            `responses. It was passed a response with origin ${origin}.`);\n    },\n    'opaque-streams-source': ({ type }) => {\n        const message = `One of the workbox-streams sources resulted in an ` +\n            `'${type}' response.`;\n        if (type === 'opaqueredirect') {\n            return (`${message} Please do not use a navigation request that results ` +\n                `in a redirect as a source.`);\n        }\n        return `${message} Please ensure your sources are CORS-enabled.`;\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messages } from './messages.js';\nimport '../../_version.js';\nconst fallback = (code, ...args) => {\n    let msg = code;\n    if (args.length > 0) {\n        msg += ` :: ${JSON.stringify(args)}`;\n    }\n    return msg;\n};\nconst generatorFunction = (code, details = {}) => {\n    const message = messages[code];\n    if (!message) {\n        throw new Error(`Unable to find message for code '${code}'.`);\n    }\n    return message(details);\n};\nexport const messageGenerator = process.env.NODE_ENV === 'production' ? fallback : generatorFunction;\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messageGenerator } from '../models/messages/messageGenerator.js';\nimport '../_version.js';\n/**\n * Workbox errors should be thrown with this class.\n * This allows use to ensure the type easily in tests,\n * helps developers identify errors from workbox\n * easily and allows use to optimise error\n * messages correctly.\n *\n * @private\n */\nclass WorkboxError extends Error {\n    /**\n     *\n     * @param {string} errorCode The error code that\n     * identifies this particular error.\n     * @param {Object=} details Any relevant arguments\n     * that will help developers identify issues should\n     * be added as a key on the context object.\n     */\n    constructor(errorCode, details) {\n        const message = messageGenerator(errorCode, details);\n        super(message);\n        this.name = errorCode;\n        this.details = details;\n    }\n}\nexport { WorkboxError };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from '../_private/WorkboxError.js';\nimport '../_version.js';\n/*\n * This method throws if the supplied value is not an array.\n * The destructed values are required to produce a meaningful error for users.\n * The destructed and restructured object is so it's clear what is\n * needed.\n */\nconst isArray = (value, details) => {\n    if (!Array.isArray(value)) {\n        throw new WorkboxError('not-an-array', details);\n    }\n};\nconst hasMethod = (object, expectedMethod, details) => {\n    const type = typeof object[expectedMethod];\n    if (type !== 'function') {\n        details['expectedMethod'] = expectedMethod;\n        throw new WorkboxError('missing-a-method', details);\n    }\n};\nconst isType = (object, expectedType, details) => {\n    if (typeof object !== expectedType) {\n        details['expectedType'] = expectedType;\n        throw new WorkboxError('incorrect-type', details);\n    }\n};\nconst isInstance = (object, \n// Need the general type to do the check later.\n// eslint-disable-next-line @typescript-eslint/ban-types\nexpectedClass, details) => {\n    if (!(object instanceof expectedClass)) {\n        details['expectedClassName'] = expectedClass.name;\n        throw new WorkboxError('incorrect-class', details);\n    }\n};\nconst isOneOf = (value, validValues, details) => {\n    if (!validValues.includes(value)) {\n        details['validValueDescription'] = `Valid values are ${JSON.stringify(validValues)}.`;\n        throw new WorkboxError('invalid-value', details);\n    }\n};\nconst isArrayOfClass = (value, \n// Need general type to do check later.\nexpectedClass, // eslint-disable-line\ndetails) => {\n    const error = new WorkboxError('not-array-of-class', details);\n    if (!Array.isArray(value)) {\n        throw error;\n    }\n    for (const item of value) {\n        if (!(item instanceof expectedClass)) {\n            throw error;\n        }\n    }\n};\nconst finalAssertExports = process.env.NODE_ENV === 'production'\n    ? null\n    : {\n        hasMethod,\n        isArray,\n        isInstance,\n        isOneOf,\n        isType,\n        isArrayOfClass,\n    };\nexport { finalAssertExports as assert };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n// Callbacks to be executed whenever there's a quota error.\n// Can't change Function type right now.\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst quotaErrorCallbacks = new Set();\nexport { quotaErrorCallbacks };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from './_private/logger.js';\nimport { assert } from './_private/assert.js';\nimport { quotaErrorCallbacks } from './models/quotaErrorCallbacks.js';\nimport './_version.js';\n/**\n * Adds a function to the set of quotaErrorCallbacks that will be executed if\n * there's a quota error.\n *\n * @param {Function} callback\n * @memberof workbox-core\n */\n// Can't change Function type\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction registerQuotaErrorCallback(callback) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isType(callback, 'function', {\n            moduleName: 'workbox-core',\n            funcName: 'register',\n            paramName: 'callback',\n        });\n    }\n    quotaErrorCallbacks.add(callback);\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Registered a callback to respond to quota errors.', callback);\n    }\n}\nexport { registerQuotaErrorCallback };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst _cacheNameDetails = {\n    googleAnalytics: 'googleAnalytics',\n    precache: 'precache-v2',\n    prefix: 'workbox',\n    runtime: 'runtime',\n    suffix: typeof registration !== 'undefined' ? registration.scope : '',\n};\nconst _createCacheName = (cacheName) => {\n    return [_cacheNameDetails.prefix, cacheName, _cacheNameDetails.suffix]\n        .filter((value) => value && value.length > 0)\n        .join('-');\n};\nconst eachCacheNameDetail = (fn) => {\n    for (const key of Object.keys(_cacheNameDetails)) {\n        fn(key);\n    }\n};\nexport const cacheNames = {\n    updateDetails: (details) => {\n        eachCacheNameDetail((key) => {\n            if (typeof details[key] === 'string') {\n                _cacheNameDetails[key] = details[key];\n            }\n        });\n    },\n    getGoogleAnalyticsName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.googleAnalytics);\n    },\n    getPrecacheName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.precache);\n    },\n    getPrefix: () => {\n        return _cacheNameDetails.prefix;\n    },\n    getRuntimeName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.runtime);\n    },\n    getSuffix: () => {\n        return _cacheNameDetails.suffix;\n    },\n};\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nfunction stripParams(fullURL, ignoreParams) {\n    const strippedURL = new URL(fullURL);\n    for (const param of ignoreParams) {\n        strippedURL.searchParams.delete(param);\n    }\n    return strippedURL.href;\n}\n/**\n * Matches an item in the cache, ignoring specific URL params. This is similar\n * to the `ignoreSearch` option, but it allows you to ignore just specific\n * params (while continuing to match on the others).\n *\n * @private\n * @param {Cache} cache\n * @param {Request} request\n * @param {Object} matchOptions\n * @param {Array<string>} ignoreParams\n * @return {Promise<Response|undefined>}\n */\nasync function cacheMatchIgnoreParams(cache, request, ignoreParams, matchOptions) {\n    const strippedRequestURL = stripParams(request.url, ignoreParams);\n    // If the request doesn't include any ignored params, match as normal.\n    if (request.url === strippedRequestURL) {\n        return cache.match(request, matchOptions);\n    }\n    // Otherwise, match by comparing keys\n    const keysOptions = Object.assign(Object.assign({}, matchOptions), { ignoreSearch: true });\n    const cacheKeys = await cache.keys(request, keysOptions);\n    for (const cacheKey of cacheKeys) {\n        const strippedCacheKeyURL = stripParams(cacheKey.url, ignoreParams);\n        if (strippedRequestURL === strippedCacheKeyURL) {\n            return cache.match(cacheKey, matchOptions);\n        }\n    }\n    return;\n}\nexport { cacheMatchIgnoreParams };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * object.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `ReadableStream`, `false` otherwise.\n *\n * @private\n */\nfunction canConstructReadableStream() {\n    if (supportStatus === undefined) {\n        // See https://github.com/GoogleChrome/workbox/issues/1473\n        try {\n            new ReadableStream({ start() { } });\n            supportStatus = true;\n        }\n        catch (error) {\n            supportStatus = false;\n        }\n    }\n    return supportStatus;\n}\nexport { canConstructReadableStream };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a new `Response` from a `response.body` stream.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `Response` from a `response.body` stream, `false` otherwise.\n *\n * @private\n */\nfunction canConstructResponseFromBodyStream() {\n    if (supportStatus === undefined) {\n        const testResponse = new Response('');\n        if ('body' in testResponse) {\n            try {\n                new Response(testResponse.body);\n                supportStatus = true;\n            }\n            catch (error) {\n                supportStatus = false;\n            }\n        }\n        supportStatus = false;\n    }\n    return supportStatus;\n}\nexport { canConstructResponseFromBodyStream };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from '../_private/logger.js';\nimport { quotaErrorCallbacks } from '../models/quotaErrorCallbacks.js';\nimport '../_version.js';\n/**\n * Runs all of the callback functions, one at a time sequentially, in the order\n * in which they were registered.\n *\n * @memberof workbox-core\n * @private\n */\nasync function executeQuotaErrorCallbacks() {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log(`About to run ${quotaErrorCallbacks.size} ` +\n            `callbacks to clean up caches.`);\n    }\n    for (const callback of quotaErrorCallbacks) {\n        await callback();\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(callback, 'is complete.');\n        }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished running callbacks.');\n    }\n}\nexport { executeQuotaErrorCallbacks };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst getFriendlyURL = (url) => {\n    const urlObj = new URL(String(url), location.href);\n    // See https://github.com/GoogleChrome/workbox/issues/2323\n    // We want to include everything, except for the origin if it's same-origin.\n    return urlObj.href.replace(new RegExp(`^${location.origin}`), '');\n};\nexport { getFriendlyURL };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns a promise that resolves and the passed number of milliseconds.\n * This utility is an async/await-friendly version of `setTimeout`.\n *\n * @param {number} ms\n * @return {Promise}\n * @private\n */\nexport function timeout(ms) {\n    return new Promise((resolve) => setTimeout(resolve, ms));\n}\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { timeout } from './timeout.js';\nimport '../_version.js';\nconst MAX_RETRY_TIME = 2000;\n/**\n * Returns a promise that resolves to a window client matching the passed\n * `resultingClientId`. For browsers that don't support `resultingClientId`\n * or if waiting for the resulting client to apper takes too long, resolve to\n * `undefined`.\n *\n * @param {string} [resultingClientId]\n * @return {Promise<Client|undefined>}\n * @private\n */\nexport async function resultingClientExists(resultingClientId) {\n    if (!resultingClientId) {\n        return;\n    }\n    let existingWindows = await self.clients.matchAll({ type: 'window' });\n    const existingWindowIds = new Set(existingWindows.map((w) => w.id));\n    let resultingWindow;\n    const startTime = performance.now();\n    // Only wait up to `MAX_RETRY_TIME` to find a matching client.\n    while (performance.now() - startTime < MAX_RETRY_TIME) {\n        existingWindows = await self.clients.matchAll({ type: 'window' });\n        resultingWindow = existingWindows.find((w) => {\n            if (resultingClientId) {\n                // If we have a `resultingClientId`, we can match on that.\n                return w.id === resultingClientId;\n            }\n            else {\n                // Otherwise match on finding a window not in `existingWindowIds`.\n                return !existingWindowIds.has(w.id);\n            }\n        });\n        if (resultingWindow) {\n            break;\n        }\n        // Sleep for 100ms and retry.\n        await timeout(100);\n    }\n    return resultingWindow;\n}\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A utility method that makes it easier to use `event.waitUntil` with\n * async functions and return the result.\n *\n * @param {ExtendableEvent} event\n * @param {Function} asyncFn\n * @return {Function}\n * @private\n */\nfunction waitUntil(event, asyncFn) {\n    const returnPromise = asyncFn();\n    event.waitUntil(returnPromise);\n    return returnPromise;\n}\nexport { waitUntil };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\n// We either expose defaults or we expose every named export.\nimport { assert } from './_private/assert.js';\nimport { cacheNames } from './_private/cacheNames.js';\nimport { cacheMatchIgnoreParams } from './_private/cacheMatchIgnoreParams.js';\nimport { canConstructReadableStream } from './_private/canConstructReadableStream.js';\nimport { canConstructResponseFromBodyStream } from './_private/canConstructResponseFromBodyStream.js';\nimport { dontWaitFor } from './_private/dontWaitFor.js';\nimport { Deferred } from './_private/Deferred.js';\nimport { executeQuotaErrorCallbacks } from './_private/executeQuotaErrorCallbacks.js';\nimport { getFriendlyURL } from './_private/getFriendlyURL.js';\nimport { logger } from './_private/logger.js';\nimport { resultingClientExists } from './_private/resultingClientExists.js';\nimport { timeout } from './_private/timeout.js';\nimport { waitUntil } from './_private/waitUntil.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\nexport { assert, cacheMatchIgnoreParams, cacheNames, canConstructReadableStream, canConstructResponseFromBodyStream, dontWaitFor, Deferred, executeQuotaErrorCallbacks, getFriendlyURL, logger, resultingClientExists, timeout, waitUntil, WorkboxError, };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames as _cacheNames } from './_private/cacheNames.js';\nimport './_version.js';\n/**\n * Get the current cache names and prefix/suffix used by Workbox.\n *\n * `cacheNames.precache` is used for precached assets,\n * `cacheNames.googleAnalytics` is used by `workbox-google-analytics` to\n * store `analytics.js`, and `cacheNames.runtime` is used for everything else.\n *\n * `cacheNames.prefix` can be used to retrieve just the current prefix value.\n * `cacheNames.suffix` can be used to retrieve just the current suffix value.\n *\n * @return {Object} An object with `precache`, `runtime`, `prefix`, and\n *     `googleAnalytics` properties.\n *\n * @memberof workbox-core\n */\nconst cacheNames = {\n    get googleAnalytics() {\n        return _cacheNames.getGoogleAnalyticsName();\n    },\n    get precache() {\n        return _cacheNames.getPrecacheName();\n    },\n    get prefix() {\n        return _cacheNames.getPrefix();\n    },\n    get runtime() {\n        return _cacheNames.getRuntimeName();\n    },\n    get suffix() {\n        return _cacheNames.getSuffix();\n    },\n};\nexport { cacheNames };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructResponseFromBodyStream } from './_private/canConstructResponseFromBodyStream.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Allows developers to copy a response and modify its `headers`, `status`,\n * or `statusText` values (the values settable via a\n * [`ResponseInit`]{@link https://developer.mozilla.org/en-US/docs/Web/API/Response/Response#Syntax}\n * object in the constructor).\n * To modify these values, pass a function as the second argument. That\n * function will be invoked with a single object with the response properties\n * `{headers, status, statusText}`. The return value of this function will\n * be used as the `ResponseInit` for the new `Response`. To change the values\n * either modify the passed parameter(s) and return it, or return a totally\n * new object.\n *\n * This method is intentionally limited to same-origin responses, regardless of\n * whether CORS was used or not.\n *\n * @param {Response} response\n * @param {Function} modifier\n * @memberof workbox-core\n */\nasync function copyResponse(response, modifier) {\n    let origin = null;\n    // If response.url isn't set, assume it's cross-origin and keep origin null.\n    if (response.url) {\n        const responseURL = new URL(response.url);\n        origin = responseURL.origin;\n    }\n    if (origin !== self.location.origin) {\n        throw new WorkboxError('cross-origin-copy-response', { origin });\n    }\n    const clonedResponse = response.clone();\n    // Create a fresh `ResponseInit` object by cloning the headers.\n    const responseInit = {\n        headers: new Headers(clonedResponse.headers),\n        status: clonedResponse.status,\n        statusText: clonedResponse.statusText,\n    };\n    // Apply any user modifications.\n    const modifiedResponseInit = modifier ? modifier(responseInit) : responseInit;\n    // Create the new response from the body stream and `ResponseInit`\n    // modifications. Note: not all browsers support the Response.body stream,\n    // so fall back to reading the entire body into memory as a blob.\n    const body = canConstructResponseFromBodyStream()\n        ? clonedResponse.body\n        : await clonedResponse.blob();\n    return new Response(body, modifiedResponseInit);\n}\nexport { copyResponse };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Claim any currently available clients once the service worker\n * becomes active. This is normally used in conjunction with `skipWaiting()`.\n *\n * @memberof workbox-core\n */\nfunction clientsClaim() {\n    self.addEventListener('activate', () => self.clients.claim());\n}\nexport { clientsClaim };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from './_private/assert.js';\nimport { cacheNames } from './_private/cacheNames.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Modifies the default cache names used by the Workbox packages.\n * Cache names are generated as `<prefix>-<Cache Name>-<suffix>`.\n *\n * @param {Object} details\n * @param {Object} [details.prefix] The string to add to the beginning of\n *     the precache and runtime cache names.\n * @param {Object} [details.suffix] The string to add to the end of\n *     the precache and runtime cache names.\n * @param {Object} [details.precache] The cache name to use for precache\n *     caching.\n * @param {Object} [details.runtime] The cache name to use for runtime caching.\n * @param {Object} [details.googleAnalytics] The cache name to use for\n *     `workbox-google-analytics` caching.\n *\n * @memberof workbox-core\n */\nfunction setCacheNameDetails(details) {\n    if (process.env.NODE_ENV !== 'production') {\n        Object.keys(details).forEach((key) => {\n            assert.isType(details[key], 'string', {\n                moduleName: 'workbox-core',\n                funcName: 'setCacheNameDetails',\n                paramName: `details.${key}`,\n            });\n        });\n        if ('precache' in details && details['precache'].length === 0) {\n            throw new WorkboxError('invalid-cache-name', {\n                cacheNameId: 'precache',\n                value: details['precache'],\n            });\n        }\n        if ('runtime' in details && details['runtime'].length === 0) {\n            throw new WorkboxError('invalid-cache-name', {\n                cacheNameId: 'runtime',\n                value: details['runtime'],\n            });\n        }\n        if ('googleAnalytics' in details &&\n            details['googleAnalytics'].length === 0) {\n            throw new WorkboxError('invalid-cache-name', {\n                cacheNameId: 'googleAnalytics',\n                value: details['googleAnalytics'],\n            });\n        }\n    }\n    cacheNames.updateDetails(details);\n}\nexport { setCacheNameDetails };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from './_private/logger.js';\nimport './_version.js';\n/**\n * This method is deprecated, and will be removed in Workbox v7.\n *\n * Calling self.skipWaiting() is equivalent, and should be used instead.\n *\n * @memberof workbox-core\n */\nfunction skipWaiting() {\n    // Just call self.skipWaiting() directly.\n    // See https://github.com/GoogleChrome/workbox/issues/2525\n    if (process.env.NODE_ENV !== 'production') {\n        logger.warn(`skipWaiting() from workbox-core is no longer recommended ` +\n            `and will be removed in Workbox v7. Using self.skipWaiting() instead ` +\n            `is equivalent.`);\n    }\n    void self.skipWaiting();\n}\nexport { skipWaiting };\n"], "names": ["self", "_", "e", "logger", "globalThis", "__WB_DISABLE_DEV_LOGS", "inGroup", "methodToColorMap", "debug", "log", "warn", "error", "groupCollapsed", "groupEnd", "print", "method", "args", "test", "navigator", "userAgent", "console", "styles", "logPrefix", "join", "api", "loggerMethods", "Object", "keys", "key", "messages", "paramName", "validValueDescription", "value", "Error", "JSON", "stringify", "moduleName", "className", "funcName", "expectedType", "classNameStr", "expectedClassName", "isReturnValueProblem", "<PERSON><PERSON><PERSON><PERSON>", "entry", "firstEntry", "secondEntry", "thrownErrorMessage", "cacheNameId", "name", "methodName", "expectedClass", "normalizedRangeHeader", "size", "start", "end", "url", "message", "status", "cacheName", "origin", "type", "generatorFunction", "code", "details", "messageGenerator", "WorkboxError", "constructor", "errorCode", "isArray", "Array", "has<PERSON><PERSON><PERSON>", "object", "isType", "isInstance", "isOneOf", "validValues", "includes", "isArrayOfClass", "item", "finalAssertExports", "quotaErrorCallbacks", "Set", "registerQuotaErrorCallback", "callback", "assert", "add", "_cacheNameDetails", "googleAnalytics", "precache", "prefix", "runtime", "suffix", "registration", "scope", "_createCacheName", "filter", "length", "eachCacheNameDetail", "fn", "cacheNames", "updateDetails", "getGoogleAnalyticsName", "userCacheName", "getPrecacheName", "getPrefix", "getRuntimeName", "getSuffix", "stripParams", "fullURL", "ignoreParams", "strippedURL", "URL", "param", "searchParams", "delete", "href", "cacheMatchIgnoreParams", "cache", "request", "matchOptions", "strippedRequestURL", "match", "keysOptions", "assign", "ignoreSearch", "cacheKeys", "cache<PERSON>ey", "strippedCacheKeyURL", "supportStatus", "canConstructReadableStream", "undefined", "ReadableStream", "canConstructResponseFromBodyStream", "testResponse", "Response", "body", "dontWait<PERSON>or", "promise", "then", "Deferred", "Promise", "resolve", "reject", "executeQuotaErrorCallbacks", "getFriendlyURL", "url<PERSON>bj", "String", "location", "replace", "RegExp", "timeout", "ms", "setTimeout", "MAX_RETRY_TIME", "resultingClientExists", "resultingClientId", "existingWindows", "clients", "matchAll", "existingWindowIds", "map", "w", "id", "resultingWindow", "startTime", "performance", "now", "find", "has", "waitUntil", "event", "asyncFn", "returnPromise", "_cacheNames", "copyResponse", "response", "modifier", "responseURL", "clonedResponse", "clone", "responseInit", "headers", "Headers", "statusText", "modifiedResponseInit", "blob", "clientsClaim", "addEventListener", "claim", "setCacheNameDetails", "for<PERSON>ach", "skipWaiting"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,oBAAD,CAAJ,IAA8BC,CAAC,EAA/B;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;IACA;IACA;IACA;IACA;IAEA,MAAMC,MAAM,GAEN,CAAC,MAAM;IACL;IACA;IACA,MAAI,EAAE,2BAA2BC,UAA7B,CAAJ,EAA8C;IAC1CJ,IAAAA,IAAI,CAACK,qBAAL,GAA6B,KAA7B;IACH;;IACD,MAAIC,OAAO,GAAG,KAAd;IACA,QAAMC,gBAAgB,GAAG;IACrBC,IAAAA,KAAK,EAAG,SADa;IAErBC,IAAAA,GAAG,EAAG,SAFe;IAGrBC,IAAAA,IAAI,EAAG,SAHc;IAIrBC,IAAAA,KAAK,EAAG,SAJa;IAKrBC,IAAAA,cAAc,EAAG,SALI;IAMrBC,IAAAA,QAAQ,EAAE,IANW;;IAAA,GAAzB;;IAQA,QAAMC,KAAK,GAAG,UAAUC,MAAV,EAAkBC,IAAlB,EAAwB;IAClC,QAAIhB,IAAI,CAACK,qBAAT,EAAgC;IAC5B;IACH;;IACD,QAAIU,MAAM,KAAK,gBAAf,EAAiC;IAC7B;IACA;IACA,UAAI,iCAAiCE,IAAjC,CAAsCC,SAAS,CAACC,SAAhD,CAAJ,EAAgE;IAC5DC,QAAAA,OAAO,CAACL,MAAD,CAAP,CAAgB,GAAGC,IAAnB;IACA;IACH;IACJ;;IACD,UAAMK,MAAM,GAAG,CACV,eAAcd,gBAAgB,CAACQ,MAAD,CAAS,EAD7B,EAEV,sBAFU,EAGV,cAHU,EAIV,mBAJU,EAKV,oBALU,CAAf,CAZkC;;IAoBlC,UAAMO,SAAS,GAAGhB,OAAO,GAAG,EAAH,GAAQ,CAAC,WAAD,EAAce,MAAM,CAACE,IAAP,CAAY,GAAZ,CAAd,CAAjC;IACAH,IAAAA,OAAO,CAACL,MAAD,CAAP,CAAgB,GAAGO,SAAnB,EAA8B,GAAGN,IAAjC;;IACA,QAAID,MAAM,KAAK,gBAAf,EAAiC;IAC7BT,MAAAA,OAAO,GAAG,IAAV;IACH;;IACD,QAAIS,MAAM,KAAK,UAAf,EAA2B;IACvBT,MAAAA,OAAO,GAAG,KAAV;IACH;IACJ,GA5BD,CAfK;;;IA6CL,QAAMkB,GAAG,GAAG,EAAZ;IACA,QAAMC,aAAa,GAAGC,MAAM,CAACC,IAAP,CAAYpB,gBAAZ,CAAtB;;IACA,OAAK,MAAMqB,GAAX,IAAkBH,aAAlB,EAAiC;IAC7B,UAAMV,MAAM,GAAGa,GAAf;;IACAJ,IAAAA,GAAG,CAACT,MAAD,CAAH,GAAc,CAAC,GAAGC,IAAJ,KAAa;IACvBF,MAAAA,KAAK,CAACC,MAAD,EAASC,IAAT,CAAL;IACH,KAFD;IAGH;;IACD,SAAOQ,GAAP;IACH,CAtDC,GAFN;;ICPA;IACA;AACA;IACA;IACA;IACA;IACA;IAEO,MAAMK,QAAQ,GAAG;IACpB,mBAAiB,CAAC;IAAEC,IAAAA,SAAF;IAAaC,IAAAA,qBAAb;IAAoCC,IAAAA;IAApC,GAAD,KAAiD;IAC9D,QAAI,CAACF,SAAD,IAAc,CAACC,qBAAnB,EAA0C;IACtC,YAAM,IAAIE,KAAJ,CAAW,4CAAX,CAAN;IACH;;IACD,WAAS,QAAOH,SAAU,wCAAlB,GACH,qBAAoBC,qBAAsB,uBADvC,GAEH,GAAEG,IAAI,CAACC,SAAL,CAAeH,KAAf,CAAsB,GAF7B;IAGH,GARmB;IASpB,kBAAgB,CAAC;IAAEI,IAAAA,UAAF;IAAcC,IAAAA,SAAd;IAAyBC,IAAAA,QAAzB;IAAmCR,IAAAA;IAAnC,GAAD,KAAoD;IAChE,QAAI,CAACM,UAAD,IAAe,CAACC,SAAhB,IAA6B,CAACC,QAA9B,IAA0C,CAACR,SAA/C,EAA0D;IACtD,YAAM,IAAIG,KAAJ,CAAW,2CAAX,CAAN;IACH;;IACD,WAAS,kBAAiBH,SAAU,gBAA5B,GACH,IAAGM,UAAW,IAAGC,SAAU,IAAGC,QAAS,uBAD5C;IAEH,GAfmB;IAgBpB,oBAAkB,CAAC;IAAEC,IAAAA,YAAF;IAAgBT,IAAAA,SAAhB;IAA2BM,IAAAA,UAA3B;IAAuCC,IAAAA,SAAvC;IAAkDC,IAAAA;IAAlD,GAAD,KAAmE;IACjF,QAAI,CAACC,YAAD,IAAiB,CAACT,SAAlB,IAA+B,CAACM,UAAhC,IAA8C,CAACE,QAAnD,EAA6D;IACzD,YAAM,IAAIL,KAAJ,CAAW,6CAAX,CAAN;IACH;;IACD,UAAMO,YAAY,GAAGH,SAAS,GAAI,GAAEA,SAAU,GAAhB,GAAqB,EAAnD;IACA,WAAS,kBAAiBP,SAAU,gBAA5B,GACH,IAAGM,UAAW,IAAGI,YAAa,EAD3B,GAEH,GAAEF,QAAS,uBAAsBC,YAAa,GAFnD;IAGH,GAxBmB;IAyBpB,qBAAmB,CAAC;IAAEE,IAAAA,iBAAF;IAAqBX,IAAAA,SAArB;IAAgCM,IAAAA,UAAhC;IAA4CC,IAAAA,SAA5C;IAAuDC,IAAAA,QAAvD;IAAiEI,IAAAA;IAAjE,GAAD,KAA8F;IAC7G,QAAI,CAACD,iBAAD,IAAsB,CAACL,UAAvB,IAAqC,CAACE,QAA1C,EAAoD;IAChD,YAAM,IAAIL,KAAJ,CAAW,8CAAX,CAAN;IACH;;IACD,UAAMO,YAAY,GAAGH,SAAS,GAAI,GAAEA,SAAU,GAAhB,GAAqB,EAAnD;;IACA,QAAIK,oBAAJ,EAA0B;IACtB,aAAS,wBAAD,GACH,IAAGN,UAAW,IAAGI,YAAa,GAAEF,QAAS,MADtC,GAEH,gCAA+BG,iBAAkB,GAFtD;IAGH;;IACD,WAAS,kBAAiBX,SAAU,gBAA5B,GACH,IAAGM,UAAW,IAAGI,YAAa,GAAEF,QAAS,MADtC,GAEH,gCAA+BG,iBAAkB,GAFtD;IAGH,GAtCmB;IAuCpB,sBAAoB,CAAC;IAAEE,IAAAA,cAAF;IAAkBb,IAAAA,SAAlB;IAA6BM,IAAAA,UAA7B;IAAyCC,IAAAA,SAAzC;IAAoDC,IAAAA;IAApD,GAAD,KAAqE;IACrF,QAAI,CAACK,cAAD,IACA,CAACb,SADD,IAEA,CAACM,UAFD,IAGA,CAACC,SAHD,IAIA,CAACC,QAJL,EAIe;IACX,YAAM,IAAIL,KAAJ,CAAW,+CAAX,CAAN;IACH;;IACD,WAAS,GAAEG,UAAW,IAAGC,SAAU,IAAGC,QAAS,kBAAvC,GACH,IAAGR,SAAU,4BAA2Ba,cAAe,WAD5D;IAEH,GAjDmB;IAkDpB,uCAAqC,CAAC;IAAEC,IAAAA;IAAF,GAAD,KAAe;IAChD,WAAS,oCAAD,GACH,qEADG,GAEH,IAAGV,IAAI,CAACC,SAAL,CAAeS,KAAf,CAAsB,iDAFtB,GAGH,sEAHG,GAIH,kBAJL;IAKH,GAxDmB;IAyDpB,2CAAyC,CAAC;IAAEC,IAAAA,UAAF;IAAcC,IAAAA;IAAd,GAAD,KAAiC;IACtE,QAAI,CAACD,UAAD,IAAe,CAACC,WAApB,EAAiC;IAC7B,YAAM,IAAIb,KAAJ,CAAW,sBAAD,GAA0B,8CAApC,CAAN;IACH;;IACD,WAAS,+BAAD,GACH,uEADG,GAEH,GAAEY,UAAW,8CAFV,GAGH,qEAHG,GAIH,iBAJL;IAKH,GAlEmB;IAmEpB,qCAAmC,CAAC;IAAEE,IAAAA;IAAF,GAAD,KAA4B;IAC3D,QAAI,CAACA,kBAAL,EAAyB;IACrB,YAAM,IAAId,KAAJ,CAAW,sBAAD,GAA0B,2CAApC,CAAN;IACH;;IACD,WAAS,gEAAD,GACH,kCAAiCc,kBAAmB,IADzD;IAEH,GAzEmB;IA0EpB,wBAAsB,CAAC;IAAEC,IAAAA,WAAF;IAAehB,IAAAA;IAAf,GAAD,KAA4B;IAC9C,QAAI,CAACgB,WAAL,EAAkB;IACd,YAAM,IAAIf,KAAJ,CAAW,yDAAX,CAAN;IACH;;IACD,WAAS,gEAAD,GACH,oBAAmBe,WAAY,iCAD5B,GAEH,IAAGd,IAAI,CAACC,SAAL,CAAeH,KAAf,CAAsB,GAF9B;IAGH,GAjFmB;IAkFpB,gDAA8C,CAAC;IAAEjB,IAAAA;IAAF,GAAD,KAAgB;IAC1D,QAAI,CAACA,MAAL,EAAa;IACT,YAAM,IAAIkB,KAAJ,CAAW,sBAAD,GACX,qDADC,CAAN;IAEH;;IACD,WAAS,4DAAD,GACH,mCAAkClB,MAAO,IAD9C;IAEH,GAzFmB;IA0FpB,2CAAyC,MAAM;IAC3C,WAAS,2DAAD,GACH,aADL;IAEH,GA7FmB;IA8FpB,yBAAuB,CAAC;IAAEkC,IAAAA;IAAF,GAAD,KAAc;IACjC,WAAQ,wCAAuCA,IAAK,WAApD;IACH,GAhGmB;IAiGpB,0BAAwB,CAAC;IAAEA,IAAAA;IAAF,GAAD,KAAc;IAClC,WAAS,mBAAkBA,IAAK,2BAAxB,GACH,mEADL;IAEH,GApGmB;IAqGpB,kCAAgC,CAAC;IAAEC,IAAAA,UAAF;IAAcpB,IAAAA;IAAd,GAAD,KAA+B;IAC3D,WAAS,QAAOoB,UAAW,uCAAnB,GACH,IAAGpB,SAAU,+BADlB;IAEH,GAxGmB;IAyGpB,4BAA0B,CAAC;IAAEM,IAAAA,UAAF;IAAcC,IAAAA,SAAd;IAAyBC,IAAAA,QAAzB;IAAmCR,IAAAA;IAAnC,GAAD,KAAoD;IAC1E,WAAS,iBAAgBA,SAAU,uCAA3B,GACH,6BAA4BM,UAAW,IAAGC,SAAU,IAAGC,QAAS,OAD7D,GAEH,oBAFL;IAGH,GA7GmB;IA8GpB,wBAAsB,CAAC;IAAEN,IAAAA,KAAF;IAASmB,IAAAA,aAAT;IAAwBf,IAAAA,UAAxB;IAAoCC,IAAAA,SAApC;IAA+CC,IAAAA,QAA/C;IAAyDR,IAAAA;IAAzD,GAAD,KAA2E;IAC7F,WAAS,iBAAgBA,SAAU,kCAA3B,GACH,IAAGqB,aAAc,wBAAuBjB,IAAI,CAACC,SAAL,CAAeH,KAAf,CAAsB,MAD3D,GAEH,4BAA2BI,UAAW,IAAGC,SAAU,IAAGC,QAAS,KAF5D,GAGH,mBAHL;IAIH,GAnHmB;IAoHpB,iCAA+B,CAAC;IAAEF,IAAAA,UAAF;IAAcC,IAAAA,SAAd;IAAyBC,IAAAA;IAAzB,GAAD,KAAyC;IACpE,WAAS,kEAAD,GACH,MAAKF,UAAW,IAAGC,SAAU,IAAGC,QAAS,EAD9C;IAEH,GAvHmB;IAwHpB,kCAAgC,CAAC;IAAEF,IAAAA,UAAF;IAAcC,IAAAA,SAAd;IAAyBC,IAAAA;IAAzB,GAAD,KAAyC;IACrE,WAAS,0DAAD,GACH,MAAKF,UAAW,IAAGC,SAAU,IAAGC,QAAS,EAD9C;IAEH,GA3HmB;IA4HpB,oBAAkB,CAAC;IAAEF,IAAAA,UAAF;IAAcE,IAAAA,QAAd;IAAwBR,IAAAA;IAAxB,GAAD,KAAyC;IACvD,QAAI,CAACA,SAAD,IAAc,CAACM,UAAf,IAA6B,CAACE,QAAlC,EAA4C;IACxC,YAAM,IAAIL,KAAJ,CAAW,6CAAX,CAAN;IACH;;IACD,WAAS,4BAA2BH,SAAU,8BAAtC,GACH,sEADG,GAEH,2BAA0BM,UAAW,IAAGE,QAAS,SAF9C,GAGH,YAHL;IAIH,GApImB;IAqIpB,2BAAyB,MAAM;IAC3B,WAAS,gDAAD,GACH,gCADL;IAEH,GAxImB;IAyIpB,qCAAmC,MAAM;IACrC,WAAS,4DAAD,GACH,kDADL;IAEH,GA5ImB;IA6IpB,+BAA6B,MAAM;IAC/B,WAAS,yDAAD,GACH,oDADL;IAEH,GAhJmB;IAiJpB,wBAAsB,CAAC;IAAEc,IAAAA;IAAF,GAAD,KAA+B;IACjD,QAAI,CAACA,qBAAL,EAA4B;IACxB,YAAM,IAAInB,KAAJ,CAAW,iDAAX,CAAN;IACH;;IACD,WAAS,iEAAD,GACH,kCAAiCmB,qBAAsB,GAD5D;IAEH,GAvJmB;IAwJpB,uBAAqB,CAAC;IAAEA,IAAAA;IAAF,GAAD,KAA+B;IAChD,QAAI,CAACA,qBAAL,EAA4B;IACxB,YAAM,IAAInB,KAAJ,CAAW,gDAAX,CAAN;IACH;;IACD,WAAS,gEAAD,GACH,+DADG,GAEH,IAAGmB,qBAAsB,GAF9B;IAGH,GA/JmB;IAgKpB,0BAAwB,CAAC;IAAEA,IAAAA;IAAF,GAAD,KAA+B;IACnD,QAAI,CAACA,qBAAL,EAA4B;IACxB,YAAM,IAAInB,KAAJ,CAAW,mDAAX,CAAN;IACH;;IACD,WAAS,kEAAD,GACH,+DADG,GAEH,IAAGmB,qBAAsB,GAF9B;IAGH,GAvKmB;IAwKpB,qBAAmB,MAAM;IACrB,WAAQ,oDAAR;IACH,GA1KmB;IA2KpB,2BAAyB,CAAC;IAAEC,IAAAA,IAAF;IAAQC,IAAAA,KAAR;IAAeC,IAAAA;IAAf,GAAD,KAA0B;IAC/C,WAAS,cAAaD,KAAM,cAAaC,GAAI,4BAArC,GACH,oDAAmDF,IAAK,SAD7D;IAEH,GA9KmB;IA+KpB,sCAAoC,CAAC;IAAEG,IAAAA,GAAF;IAAOzC,IAAAA;IAAP,GAAD,KAAqB;IACrD,WAAS,oBAAmByC,GAAI,sBAAqBzC,MAAO,gBAApD,GACH,oCADL;IAEH,GAlLmB;IAmLpB,gCAA8B,CAAC;IAAEyC,IAAAA;IAAF,GAAD,KAAa;IACvC,WAAS,kCAAiCA,GAAI,6BAAtC,GACH,UADL;IAEH,GAtLmB;IAuLpB,iBAAe,CAAC;IAAEA,IAAAA,GAAF;IAAO7C,IAAAA;IAAP,GAAD,KAAoB;IAC/B,QAAI8C,OAAO,GAAI,mDAAkDD,GAAI,IAArE;;IACA,QAAI7C,KAAJ,EAAW;IACP8C,MAAAA,OAAO,IAAK,4BAA2B9C,KAAM,GAA7C;IACH;;IACD,WAAO8C,OAAP;IACH,GA7LmB;IA8LpB,6BAA2B,CAAC;IAAED,IAAAA,GAAF;IAAOE,IAAAA;IAAP,GAAD,KAAqB;IAC5C,WAAS,+BAA8BF,GAAI,UAAnC,IACHE,MAAM,GAAI,2BAA0BA,MAAO,GAArC,GAA2C,GAD9C,CAAR;IAEH,GAjMmB;IAkMpB,uBAAqB,CAAC;IAAEF,IAAAA;IAAF,GAAD,KAAa;IAC9B,WAAS,4BAA2BA,GAAI,iCAAhC,GACH,gEADL;IAEH,GArMmB;IAsMpB,+CAA6C,CAAC;IAAEA,IAAAA;IAAF,GAAD,KAAa;IACtD,WAAS,+BAAD,GACH,uEADG,GAEH,GAAEA,GAAI,8DAFX;IAGH,GA1MmB;IA2MpB,4BAA0B,CAAC;IAAEG,IAAAA,SAAF;IAAaH,IAAAA;IAAb,GAAD,KAAwB;IAC9C,WAAQ,0CAAyCG,SAAU,QAAOH,GAAI,GAAtE;IACH,GA7MmB;IA8MpB,gCAA8B,CAAC;IAAEI,IAAAA;IAAF,GAAD,KAAgB;IAC1C,WAAS,gEAAD,GACH,mDAAkDA,MAAO,GAD9D;IAEH,GAjNmB;IAkNpB,2BAAyB,CAAC;IAAEC,IAAAA;IAAF,GAAD,KAAc;IACnC,UAAMJ,OAAO,GAAI,oDAAD,GACX,IAAGI,IAAK,aADb;;IAEA,QAAIA,IAAI,KAAK,gBAAb,EAA+B;IAC3B,aAAS,GAAEJ,OAAQ,uDAAX,GACH,4BADL;IAEH;;IACD,WAAQ,GAAEA,OAAQ,+CAAlB;IACH;IA1NmB,CAAjB;;ICRP;IACA;AACA;IACA;IACA;IACA;IACA;;IAUA,MAAMK,iBAAiB,GAAG,CAACC,IAAD,EAAOC,OAAO,GAAG,EAAjB,KAAwB;IAC9C,QAAMP,OAAO,GAAG5B,QAAQ,CAACkC,IAAD,CAAxB;;IACA,MAAI,CAACN,OAAL,EAAc;IACV,UAAM,IAAIxB,KAAJ,CAAW,oCAAmC8B,IAAK,IAAnD,CAAN;IACH;;IACD,SAAON,OAAO,CAACO,OAAD,CAAd;IACH,CAND;;IAOO,MAAMC,gBAAgB,GAAsDH,iBAA5E;;ICvBP;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMI,YAAN,SAA2BjC,KAA3B,CAAiC;IAC7B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACIkC,EAAAA,WAAW,CAACC,SAAD,EAAYJ,OAAZ,EAAqB;IAC5B,UAAMP,OAAO,GAAGQ,gBAAgB,CAACG,SAAD,EAAYJ,OAAZ,CAAhC;IACA,UAAMP,OAAN;IACA,SAAKR,IAAL,GAAYmB,SAAZ;IACA,SAAKJ,OAAL,GAAeA,OAAf;IACH;;IAd4B;;IClBjC;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMK,OAAO,GAAG,CAACrC,KAAD,EAAQgC,OAAR,KAAoB;IAChC,MAAI,CAACM,KAAK,CAACD,OAAN,CAAcrC,KAAd,CAAL,EAA2B;IACvB,UAAM,IAAIkC,YAAJ,CAAiB,cAAjB,EAAiCF,OAAjC,CAAN;IACH;IACJ,CAJD;;IAKA,MAAMO,SAAS,GAAG,CAACC,MAAD,EAAS7B,cAAT,EAAyBqB,OAAzB,KAAqC;IACnD,QAAMH,IAAI,GAAG,OAAOW,MAAM,CAAC7B,cAAD,CAA1B;;IACA,MAAIkB,IAAI,KAAK,UAAb,EAAyB;IACrBG,IAAAA,OAAO,CAAC,gBAAD,CAAP,GAA4BrB,cAA5B;IACA,UAAM,IAAIuB,YAAJ,CAAiB,kBAAjB,EAAqCF,OAArC,CAAN;IACH;IACJ,CAND;;IAOA,MAAMS,MAAM,GAAG,CAACD,MAAD,EAASjC,YAAT,EAAuByB,OAAvB,KAAmC;IAC9C,MAAI,OAAOQ,MAAP,KAAkBjC,YAAtB,EAAoC;IAChCyB,IAAAA,OAAO,CAAC,cAAD,CAAP,GAA0BzB,YAA1B;IACA,UAAM,IAAI2B,YAAJ,CAAiB,gBAAjB,EAAmCF,OAAnC,CAAN;IACH;IACJ,CALD;;IAMA,MAAMU,UAAU,GAAG,CAACF,MAAD;IAEnB;IACArB,aAHmB,EAGJa,OAHI,KAGQ;IACvB,MAAI,EAAEQ,MAAM,YAAYrB,aAApB,CAAJ,EAAwC;IACpCa,IAAAA,OAAO,CAAC,mBAAD,CAAP,GAA+Bb,aAAa,CAACF,IAA7C;IACA,UAAM,IAAIiB,YAAJ,CAAiB,iBAAjB,EAAoCF,OAApC,CAAN;IACH;IACJ,CARD;;IASA,MAAMW,OAAO,GAAG,CAAC3C,KAAD,EAAQ4C,WAAR,EAAqBZ,OAArB,KAAiC;IAC7C,MAAI,CAACY,WAAW,CAACC,QAAZ,CAAqB7C,KAArB,CAAL,EAAkC;IAC9BgC,IAAAA,OAAO,CAAC,uBAAD,CAAP,GAAoC,oBAAmB9B,IAAI,CAACC,SAAL,CAAeyC,WAAf,CAA4B,GAAnF;IACA,UAAM,IAAIV,YAAJ,CAAiB,eAAjB,EAAkCF,OAAlC,CAAN;IACH;IACJ,CALD;;IAMA,MAAMc,cAAc,GAAG,CAAC9C,KAAD;IAEvBmB,aAFuB;IAGvBa,OAHuB,KAGX;IACR,QAAMrD,KAAK,GAAG,IAAIuD,YAAJ,CAAiB,oBAAjB,EAAuCF,OAAvC,CAAd;;IACA,MAAI,CAACM,KAAK,CAACD,OAAN,CAAcrC,KAAd,CAAL,EAA2B;IACvB,UAAMrB,KAAN;IACH;;IACD,OAAK,MAAMoE,IAAX,IAAmB/C,KAAnB,EAA0B;IACtB,QAAI,EAAE+C,IAAI,YAAY5B,aAAlB,CAAJ,EAAsC;IAClC,YAAMxC,KAAN;IACH;IACJ;IACJ,CAbD;;IAcA,MAAMqE,kBAAkB,GAElB;IACET,EAAAA,SADF;IAEEF,EAAAA,OAFF;IAGEK,EAAAA,UAHF;IAIEC,EAAAA,OAJF;IAKEF,EAAAA,MALF;IAMEK,EAAAA;IANF,CAFN;;IC9DA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;;IACA,MAAMG,mBAAmB,GAAG,IAAIC,GAAJ,EAA5B;;ICXA;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,0BAAT,CAAoCC,QAApC,EAA8C;IAC1C,EAA2C;IACvCC,IAAAA,kBAAM,CAACZ,MAAP,CAAcW,QAAd,EAAwB,UAAxB,EAAoC;IAChChD,MAAAA,UAAU,EAAE,cADoB;IAEhCE,MAAAA,QAAQ,EAAE,UAFsB;IAGhCR,MAAAA,SAAS,EAAE;IAHqB,KAApC;IAKH;;IACDmD,EAAAA,mBAAmB,CAACK,GAApB,CAAwBF,QAAxB;;IACA,EAA2C;IACvCjF,IAAAA,MAAM,CAACM,GAAP,CAAW,mDAAX,EAAgE2E,QAAhE;IACH;IACJ;;IChCD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA,MAAMG,iBAAiB,GAAG;IACtBC,EAAAA,eAAe,EAAE,iBADK;IAEtBC,EAAAA,QAAQ,EAAE,aAFY;IAGtBC,EAAAA,MAAM,EAAE,SAHc;IAItBC,EAAAA,OAAO,EAAE,SAJa;IAKtBC,EAAAA,MAAM,EAAE,OAAOC,YAAP,KAAwB,WAAxB,GAAsCA,YAAY,CAACC,KAAnD,GAA2D;IAL7C,CAA1B;;IAOA,MAAMC,gBAAgB,GAAIpC,SAAD,IAAe;IACpC,SAAO,CAAC4B,iBAAiB,CAACG,MAAnB,EAA2B/B,SAA3B,EAAsC4B,iBAAiB,CAACK,MAAxD,EACFI,MADE,CACMhE,KAAD,IAAWA,KAAK,IAAIA,KAAK,CAACiE,MAAN,GAAe,CADxC,EAEF1E,IAFE,CAEG,GAFH,CAAP;IAGH,CAJD;;IAKA,MAAM2E,mBAAmB,GAAIC,EAAD,IAAQ;IAChC,OAAK,MAAMvE,GAAX,IAAkBF,MAAM,CAACC,IAAP,CAAY4D,iBAAZ,CAAlB,EAAkD;IAC9CY,IAAAA,EAAE,CAACvE,GAAD,CAAF;IACH;IACJ,CAJD;;IAKO,MAAMwE,YAAU,GAAG;IACtBC,EAAAA,aAAa,EAAGrC,OAAD,IAAa;IACxBkC,IAAAA,mBAAmB,CAAEtE,GAAD,IAAS;IACzB,UAAI,OAAOoC,OAAO,CAACpC,GAAD,CAAd,KAAwB,QAA5B,EAAsC;IAClC2D,QAAAA,iBAAiB,CAAC3D,GAAD,CAAjB,GAAyBoC,OAAO,CAACpC,GAAD,CAAhC;IACH;IACJ,KAJkB,CAAnB;IAKH,GAPqB;IAQtB0E,EAAAA,sBAAsB,EAAGC,aAAD,IAAmB;IACvC,WAAOA,aAAa,IAAIR,gBAAgB,CAACR,iBAAiB,CAACC,eAAnB,CAAxC;IACH,GAVqB;IAWtBgB,EAAAA,eAAe,EAAGD,aAAD,IAAmB;IAChC,WAAOA,aAAa,IAAIR,gBAAgB,CAACR,iBAAiB,CAACE,QAAnB,CAAxC;IACH,GAbqB;IActBgB,EAAAA,SAAS,EAAE,MAAM;IACb,WAAOlB,iBAAiB,CAACG,MAAzB;IACH,GAhBqB;IAiBtBgB,EAAAA,cAAc,EAAGH,aAAD,IAAmB;IAC/B,WAAOA,aAAa,IAAIR,gBAAgB,CAACR,iBAAiB,CAACI,OAAnB,CAAxC;IACH,GAnBqB;IAoBtBgB,EAAAA,SAAS,EAAE,MAAM;IACb,WAAOpB,iBAAiB,CAACK,MAAzB;IACH;IAtBqB,CAAnB;;ICzBP;IACA;IACA;IACA;IACA;IACA;;IAEA,SAASgB,WAAT,CAAqBC,OAArB,EAA8BC,YAA9B,EAA4C;IACxC,QAAMC,WAAW,GAAG,IAAIC,GAAJ,CAAQH,OAAR,CAApB;;IACA,OAAK,MAAMI,KAAX,IAAoBH,YAApB,EAAkC;IAC9BC,IAAAA,WAAW,CAACG,YAAZ,CAAyBC,MAAzB,CAAgCF,KAAhC;IACH;;IACD,SAAOF,WAAW,CAACK,IAAnB;IACH;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,eAAeC,sBAAf,CAAsCC,KAAtC,EAA6CC,OAA7C,EAAsDT,YAAtD,EAAoEU,YAApE,EAAkF;IAC9E,QAAMC,kBAAkB,GAAGb,WAAW,CAACW,OAAO,CAAC/D,GAAT,EAAcsD,YAAd,CAAtC,CAD8E;;IAG9E,MAAIS,OAAO,CAAC/D,GAAR,KAAgBiE,kBAApB,EAAwC;IACpC,WAAOH,KAAK,CAACI,KAAN,CAAYH,OAAZ,EAAqBC,YAArB,CAAP;IACH,GAL6E;;;IAO9E,QAAMG,WAAW,GAAGjG,MAAM,CAACkG,MAAP,CAAclG,MAAM,CAACkG,MAAP,CAAc,EAAd,EAAkBJ,YAAlB,CAAd,EAA+C;IAAEK,IAAAA,YAAY,EAAE;IAAhB,GAA/C,CAApB;IACA,QAAMC,SAAS,GAAG,MAAMR,KAAK,CAAC3F,IAAN,CAAW4F,OAAX,EAAoBI,WAApB,CAAxB;;IACA,OAAK,MAAMI,QAAX,IAAuBD,SAAvB,EAAkC;IAC9B,UAAME,mBAAmB,GAAGpB,WAAW,CAACmB,QAAQ,CAACvE,GAAV,EAAesD,YAAf,CAAvC;;IACA,QAAIW,kBAAkB,KAAKO,mBAA3B,EAAgD;IAC5C,aAAOV,KAAK,CAACI,KAAN,CAAYK,QAAZ,EAAsBP,YAAtB,CAAP;IACH;IACJ;;IACD;IACH;;IC1CD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA,IAAIS,eAAJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,0BAAT,GAAsC;IAClC,MAAID,eAAa,KAAKE,SAAtB,EAAiC;IAC7B;IACA,QAAI;IACA,UAAIC,cAAJ,CAAmB;IAAE9E,QAAAA,KAAK,GAAG;;IAAV,OAAnB;IACA2E,MAAAA,eAAa,GAAG,IAAhB;IACH,KAHD,CAIA,OAAOtH,KAAP,EAAc;IACVsH,MAAAA,eAAa,GAAG,KAAhB;IACH;IACJ;;IACD,SAAOA,eAAP;IACH;;IC/BD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA,IAAIA,aAAJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASI,kCAAT,GAA8C;IAC1C,MAAIJ,aAAa,KAAKE,SAAtB,EAAiC;IAC7B,UAAMG,YAAY,GAAG,IAAIC,QAAJ,CAAa,EAAb,CAArB;;IACA,QAAI,UAAUD,YAAd,EAA4B;IACxB,UAAI;IACA,YAAIC,QAAJ,CAAaD,YAAY,CAACE,IAA1B;IACAP,QAAAA,aAAa,GAAG,IAAhB;IACH,OAHD,CAIA,OAAOtH,KAAP,EAAc;IACVsH,QAAAA,aAAa,GAAG,KAAhB;IACH;IACJ;;IACDA,IAAAA,aAAa,GAAG,KAAhB;IACH;;IACD,SAAOA,aAAP;IACH;;ICjCD;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;;IACO,SAASQ,WAAT,CAAqBC,OAArB,EAA8B;IACjC;IACA,OAAKA,OAAO,CAACC,IAAR,CAAa,MAAM,EAAnB,CAAL;IACH;;ICfD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMC,QAAN,CAAe;IACX;IACJ;IACA;IACIzE,EAAAA,WAAW,GAAG;IACV,SAAKuE,OAAL,GAAe,IAAIG,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;IAC5C,WAAKD,OAAL,GAAeA,OAAf;IACA,WAAKC,MAAL,GAAcA,MAAd;IACH,KAHc,CAAf;IAIH;;IATU;;IChBf;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,eAAeC,0BAAf,GAA4C;IACxC,EAA2C;IACvC7I,IAAAA,MAAM,CAACM,GAAP,CAAY,gBAAewE,mBAAmB,CAAC5B,IAAK,GAAzC,GACN,+BADL;IAEH;;IACD,OAAK,MAAM+B,QAAX,IAAuBH,mBAAvB,EAA4C;IACxC,UAAMG,QAAQ,EAAd;;IACA,IAA2C;IACvCjF,MAAAA,MAAM,CAACM,GAAP,CAAW2E,QAAX,EAAqB,cAArB;IACH;IACJ;;IACD,EAA2C;IACvCjF,IAAAA,MAAM,CAACM,GAAP,CAAW,6BAAX;IACH;IACJ;;IC/BD;IACA;AACA;IACA;IACA;IACA;IACA;;IAEA,MAAMwI,cAAc,GAAIzF,GAAD,IAAS;IAC5B,QAAM0F,MAAM,GAAG,IAAIlC,GAAJ,CAAQmC,MAAM,CAAC3F,GAAD,CAAd,EAAqB4F,QAAQ,CAAChC,IAA9B,CAAf,CAD4B;IAG5B;;IACA,SAAO8B,MAAM,CAAC9B,IAAP,CAAYiC,OAAZ,CAAoB,IAAIC,MAAJ,CAAY,IAAGF,QAAQ,CAACxF,MAAO,EAA/B,CAApB,EAAuD,EAAvD,CAAP;IACH,CALD;;ICRA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACO,SAAS2F,OAAT,CAAiBC,EAAjB,EAAqB;IACxB,SAAO,IAAIX,OAAJ,CAAaC,OAAD,IAAaW,UAAU,CAACX,OAAD,EAAUU,EAAV,CAAnC,CAAP;IACH;;ICjBD;IACA;IACA;IACA;IACA;IACA;IAGA,MAAME,cAAc,GAAG,IAAvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACO,eAAeC,qBAAf,CAAqCC,iBAArC,EAAwD;IAC3D,MAAI,CAACA,iBAAL,EAAwB;IACpB;IACH;;IACD,MAAIC,eAAe,GAAG,MAAM7J,IAAI,CAAC8J,OAAL,CAAaC,QAAb,CAAsB;IAAElG,IAAAA,IAAI,EAAE;IAAR,GAAtB,CAA5B;IACA,QAAMmG,iBAAiB,GAAG,IAAI9E,GAAJ,CAAQ2E,eAAe,CAACI,GAAhB,CAAqBC,CAAD,IAAOA,CAAC,CAACC,EAA7B,CAAR,CAA1B;IACA,MAAIC,eAAJ;IACA,QAAMC,SAAS,GAAGC,WAAW,CAACC,GAAZ,EAAlB,CAP2D;;IAS3D,SAAOD,WAAW,CAACC,GAAZ,KAAoBF,SAApB,GAAgCX,cAAvC,EAAuD;IACnDG,IAAAA,eAAe,GAAG,MAAM7J,IAAI,CAAC8J,OAAL,CAAaC,QAAb,CAAsB;IAAElG,MAAAA,IAAI,EAAE;IAAR,KAAtB,CAAxB;IACAuG,IAAAA,eAAe,GAAGP,eAAe,CAACW,IAAhB,CAAsBN,CAAD,IAAO;IAC1C,UAAIN,iBAAJ,EAAuB;IACnB;IACA,eAAOM,CAAC,CAACC,EAAF,KAASP,iBAAhB;IACH,OAHD,MAIK;IACD;IACA,eAAO,CAACI,iBAAiB,CAACS,GAAlB,CAAsBP,CAAC,CAACC,EAAxB,CAAR;IACH;IACJ,KATiB,CAAlB;;IAUA,QAAIC,eAAJ,EAAqB;IACjB;IACH,KAdkD;;;IAgBnD,UAAMb,OAAO,CAAC,GAAD,CAAb;IACH;;IACD,SAAOa,eAAP;IACH;;IC/CD;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASM,SAAT,CAAmBC,KAAnB,EAA0BC,OAA1B,EAAmC;IAC/B,QAAMC,aAAa,GAAGD,OAAO,EAA7B;IACAD,EAAAA,KAAK,CAACD,SAAN,CAAgBG,aAAhB;IACA,SAAOA,aAAP;IACH;;ICpBD;IACA;AACA;IACA;IACA;IACA;IACA;;;;;;;;;;;;;;;;;;;;ICNA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;UACMzE,UAAU,GAAG;IACf,MAAIZ,eAAJ,GAAsB;IAClB,WAAOsF,YAAW,CAACxE,sBAAZ,EAAP;IACH,GAHc;;IAIf,MAAIb,QAAJ,GAAe;IACX,WAAOqF,YAAW,CAACtE,eAAZ,EAAP;IACH,GANc;;IAOf,MAAId,MAAJ,GAAa;IACT,WAAOoF,YAAW,CAACrE,SAAZ,EAAP;IACH,GATc;;IAUf,MAAId,OAAJ,GAAc;IACV,WAAOmF,YAAW,CAACpE,cAAZ,EAAP;IACH,GAZc;;IAaf,MAAId,MAAJ,GAAa;IACT,WAAOkF,YAAW,CAACnE,SAAZ,EAAP;IACH;;IAfc;;ICxBnB;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,eAAeoE,YAAf,CAA4BC,QAA5B,EAAsCC,QAAtC,EAAgD;IAC5C,MAAIrH,MAAM,GAAG,IAAb,CAD4C;;IAG5C,MAAIoH,QAAQ,CAACxH,GAAb,EAAkB;IACd,UAAM0H,WAAW,GAAG,IAAIlE,GAAJ,CAAQgE,QAAQ,CAACxH,GAAjB,CAApB;IACAI,IAAAA,MAAM,GAAGsH,WAAW,CAACtH,MAArB;IACH;;IACD,MAAIA,MAAM,KAAK5D,IAAI,CAACoJ,QAAL,CAAcxF,MAA7B,EAAqC;IACjC,UAAM,IAAIM,YAAJ,CAAiB,4BAAjB,EAA+C;IAAEN,MAAAA;IAAF,KAA/C,CAAN;IACH;;IACD,QAAMuH,cAAc,GAAGH,QAAQ,CAACI,KAAT,EAAvB,CAV4C;;IAY5C,QAAMC,YAAY,GAAG;IACjBC,IAAAA,OAAO,EAAE,IAAIC,OAAJ,CAAYJ,cAAc,CAACG,OAA3B,CADQ;IAEjB5H,IAAAA,MAAM,EAAEyH,cAAc,CAACzH,MAFN;IAGjB8H,IAAAA,UAAU,EAAEL,cAAc,CAACK;IAHV,GAArB,CAZ4C;;IAkB5C,QAAMC,oBAAoB,GAAGR,QAAQ,GAAGA,QAAQ,CAACI,YAAD,CAAX,GAA4BA,YAAjE,CAlB4C;IAoB5C;IACA;;IACA,QAAM7C,IAAI,GAAGH,kCAAkC,KACzC8C,cAAc,CAAC3C,IAD0B,GAEzC,MAAM2C,cAAc,CAACO,IAAf,EAFZ;IAGA,SAAO,IAAInD,QAAJ,CAAaC,IAAb,EAAmBiD,oBAAnB,CAAP;IACH;;ICvDD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASE,YAAT,GAAwB;IACpB3L,EAAAA,IAAI,CAAC4L,gBAAL,CAAsB,UAAtB,EAAkC,MAAM5L,IAAI,CAAC8J,OAAL,CAAa+B,KAAb,EAAxC;IACH;;IChBD;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,mBAAT,CAA6B9H,OAA7B,EAAsC;IAClC,EAA2C;IACvCtC,IAAAA,MAAM,CAACC,IAAP,CAAYqC,OAAZ,EAAqB+H,OAArB,CAA8BnK,GAAD,IAAS;IAClCyD,MAAAA,kBAAM,CAACZ,MAAP,CAAcT,OAAO,CAACpC,GAAD,CAArB,EAA4B,QAA5B,EAAsC;IAClCQ,QAAAA,UAAU,EAAE,cADsB;IAElCE,QAAAA,QAAQ,EAAE,qBAFwB;IAGlCR,QAAAA,SAAS,EAAG,WAAUF,GAAI;IAHQ,OAAtC;IAKH,KAND;;IAOA,QAAI,cAAcoC,OAAd,IAAyBA,OAAO,CAAC,UAAD,CAAP,CAAoBiC,MAApB,KAA+B,CAA5D,EAA+D;IAC3D,YAAM,IAAI/B,YAAJ,CAAiB,oBAAjB,EAAuC;IACzClB,QAAAA,WAAW,EAAE,UAD4B;IAEzChB,QAAAA,KAAK,EAAEgC,OAAO,CAAC,UAAD;IAF2B,OAAvC,CAAN;IAIH;;IACD,QAAI,aAAaA,OAAb,IAAwBA,OAAO,CAAC,SAAD,CAAP,CAAmBiC,MAAnB,KAA8B,CAA1D,EAA6D;IACzD,YAAM,IAAI/B,YAAJ,CAAiB,oBAAjB,EAAuC;IACzClB,QAAAA,WAAW,EAAE,SAD4B;IAEzChB,QAAAA,KAAK,EAAEgC,OAAO,CAAC,SAAD;IAF2B,OAAvC,CAAN;IAIH;;IACD,QAAI,qBAAqBA,OAArB,IACAA,OAAO,CAAC,iBAAD,CAAP,CAA2BiC,MAA3B,KAAsC,CAD1C,EAC6C;IACzC,YAAM,IAAI/B,YAAJ,CAAiB,oBAAjB,EAAuC;IACzClB,QAAAA,WAAW,EAAE,iBAD4B;IAEzChB,QAAAA,KAAK,EAAEgC,OAAO,CAAC,iBAAD;IAF2B,OAAvC,CAAN;IAIH;IACJ;;IACDoC,EAAAA,YAAU,CAACC,aAAX,CAAyBrC,OAAzB;IACH;;IC1DD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASgI,WAAT,GAAuB;IACnB;IACA;IACA,EAA2C;IACvC7L,IAAAA,MAAM,CAACO,IAAP,CAAa,2DAAD,GACP,sEADO,GAEP,gBAFL;IAGH;;IACD,OAAKV,IAAI,CAACgM,WAAL,EAAL;IACH;;;;;;;;;;;;;;;;"}