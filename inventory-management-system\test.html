<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StockFlow Pro - Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Loading Screen -->
    <div id="loadingScreen" class="fixed inset-0 gradient-bg flex items-center justify-center z-50">
        <div class="text-center text-white">
            <div class="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h2 class="text-2xl font-bold">Loading StockFlow Pro...</h2>
            <p class="opacity-80">Initializing your inventory management system</p>
            <p id="debugInfo" class="text-sm mt-4 opacity-60">Checking dependencies...</p>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="loginScreen" class="fixed inset-0 gradient-bg flex items-center justify-center z-40 hidden">
        <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="package" class="w-8 h-8 text-purple-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-900">StockFlow Pro</h1>
                <p class="text-gray-600">Complete Inventory Management System</p>
            </div>

            <!-- Quick Login Options -->
            <div class="grid grid-cols-2 gap-3 mb-6">
                <button onclick="quickLogin('admin', 'admin123')" class="bg-purple-100 text-purple-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-purple-200 transition-colors">
                    <i data-lucide="shield" class="w-4 h-4 inline mr-1"></i>
                    Admin Login
                </button>
                <button onclick="quickLogin('employee', 'emp123')" class="bg-blue-100 text-blue-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors">
                    <i data-lucide="user" class="w-4 h-4 inline mr-1"></i>
                    Employee Login
                </button>
            </div>

            <form id="loginForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                    <input type="text" id="loginUsername" required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="loginPassword" required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                </div>
                <button type="submit" class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition duration-200 font-semibold">
                    Sign In
                </button>
            </form>
            
            <div id="loginResult" class="mt-4"></div>
        </div>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="hidden">
        <div class="p-8 text-center">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Welcome to StockFlow Pro!</h1>
            <p class="text-gray-600 mb-6">Your inventory management system is ready.</p>
            <button onclick="logout()" class="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors">
                Logout
            </button>
        </div>
    </div>

    <script>
        console.log('Script starting...');
        
        // Debug function
        function updateDebugInfo(message) {
            const debugElement = document.getElementById('debugInfo');
            if (debugElement) {
                debugElement.textContent = message;
            }
            console.log('Debug:', message);
        }

        // Check dependencies
        updateDebugInfo('Checking Tailwind CSS...');
        setTimeout(() => {
            updateDebugInfo('Checking Axios...');
            if (typeof axios === 'undefined') {
                updateDebugInfo('ERROR: Axios not loaded!');
                return;
            }
            
            setTimeout(() => {
                updateDebugInfo('Checking Lucide...');
                if (typeof lucide === 'undefined') {
                    updateDebugInfo('ERROR: Lucide not loaded!');
                    return;
                }
                
                setTimeout(() => {
                    updateDebugInfo('All dependencies loaded! Initializing...');
                    
                    // Initialize icons
                    lucide.createIcons();
                    
                    setTimeout(() => {
                        updateDebugInfo('Ready!');
                        
                        // Hide loading screen
                        document.getElementById('loadingScreen').classList.add('hidden');
                        document.getElementById('loginScreen').classList.remove('hidden');
                        
                    }, 500);
                }, 500);
            }, 500);
        }, 500);

        // Simple login function
        function quickLogin(username, password) {
            document.getElementById('loginUsername').value = username;
            document.getElementById('loginPassword').value = password;
            
            // Simulate login
            document.getElementById('loginResult').innerHTML = 
                '<div class="p-3 bg-green-100 border border-green-400 text-green-700 rounded">Login successful!</div>';
            
            setTimeout(() => {
                document.getElementById('loginScreen').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
            }, 1000);
        }

        function logout() {
            document.getElementById('mainApp').classList.add('hidden');
            document.getElementById('loginScreen').classList.remove('hidden');
            document.getElementById('loginResult').innerHTML = '';
            document.getElementById('loginUsername').value = '';
            document.getElementById('loginPassword').value = '';
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            quickLogin(username, password);
        });
    </script>
</body>
</html>
