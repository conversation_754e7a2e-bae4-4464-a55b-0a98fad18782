const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const mysql = require('mysql2');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Database connection
const db = mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'inventory_db'
});

// In-memory storage (since we don't have MySQL)
let users = [];
let products = [];
let suppliers = [];
let customers = [];
let categories = [];
let invoices = [];
let invoiceItems = [];
let stockMovements = [];
let userLogs = [];
let settings = {
  company_name: 'StockFlow Inc.',
  company_address: '123 Business Street, City, State 12345',
  company_phone: '******-567-8900',
  company_email: '<EMAIL>',
  gst_number: 'GST123456789',
  currency_symbol: '$',
  tax_rate: 18,
  low_stock_threshold: 10,
  invoice_prefix: 'INV'
};

let nextUserId = 1;
let nextProductId = 1;
let nextSupplierId = 1;
let nextCustomerId = 1;
let nextCategoryId = 1;
let nextInvoiceId = 1;
let nextStockMovementId = 1;

// Create default data
const initializeDefaultData = async () => {
  // Create default admin user
  const hashedPassword = await bcrypt.hash('admin123', 10);
  users.push({
    id: nextUserId++,
    username: 'admin',
    password: hashedPassword,
    role: 'admin',
    email: '<EMAIL>',
    created_at: new Date()
  });

  // Create sample employee
  const empPassword = await bcrypt.hash('emp123', 10);
  users.push({
    id: nextUserId++,
    username: 'employee',
    password: empPassword,
    role: 'employee',
    email: '<EMAIL>',
    created_at: new Date()
  });

  // Create default categories
  categories.push(
    { id: nextCategoryId++, name: 'Electronics', description: 'Electronic devices and accessories', created_at: new Date() },
    { id: nextCategoryId++, name: 'Clothing', description: 'Apparel and fashion items', created_at: new Date() },
    { id: nextCategoryId++, name: 'Food & Beverages', description: 'Food items and drinks', created_at: new Date() },
    { id: nextCategoryId++, name: 'Books', description: 'Books and educational materials', created_at: new Date() },
    { id: nextCategoryId++, name: 'Home & Garden', description: 'Home improvement and garden supplies', created_at: new Date() }
  );

  // Create sample suppliers
  suppliers.push(
    {
      id: nextSupplierId++,
      name: 'TechCorp Supplies',
      contact_person: 'John Smith',
      email: '<EMAIL>',
      phone: '******-0101',
      address: '456 Tech Avenue, Silicon Valley, CA',
      gst_number: 'GST001',
      created_at: new Date()
    },
    {
      id: nextSupplierId++,
      name: 'Fashion Hub',
      contact_person: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '******-0102',
      address: '789 Fashion Street, New York, NY',
      gst_number: 'GST002',
      created_at: new Date()
    }
  );

  // Create sample customers
  customers.push(
    {
      id: nextCustomerId++,
      name: 'Walk-in Customer',
      email: '',
      phone: '',
      address: '',
      gst_number: '',
      created_at: new Date()
    },
    {
      id: nextCustomerId++,
      name: 'ABC Corporation',
      email: '<EMAIL>',
      phone: '******-0201',
      address: '123 Business Plaza, Downtown',
      gst_number: 'GST101',
      created_at: new Date()
    }
  );

  // Create sample products
  products.push(
    {
      id: nextProductId++,
      name: 'Wireless Headphones',
      description: 'High-quality wireless headphones with noise cancellation',
      category: 'Electronics',
      price: 99.99,
      quantity: 50,
      supplier_id: 1,
      barcode: '1234567890123',
      expiry_date: null,
      image_url: null,
      created_at: new Date()
    },
    {
      id: nextProductId++,
      name: 'Cotton T-Shirt',
      description: 'Comfortable cotton t-shirt in various sizes',
      category: 'Clothing',
      price: 19.99,
      quantity: 100,
      supplier_id: 2,
      barcode: '1234567890124',
      expiry_date: null,
      image_url: null,
      created_at: new Date()
    },
    {
      id: nextProductId++,
      name: 'Organic Coffee Beans',
      description: 'Premium organic coffee beans - 1kg pack',
      category: 'Food & Beverages',
      price: 24.99,
      quantity: 5, // Low stock for testing alerts
      supplier_id: 1,
      barcode: '1234567890125',
      expiry_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      image_url: null,
      created_at: new Date()
    }
  );

  console.log('Default data initialized:');
  console.log('- Admin user: username=admin, password=admin123');
  console.log('- Employee user: username=employee, password=emp123');
  console.log('- Sample categories, suppliers, customers, and products created');
};

// Initialize default data
initializeDefaultData();

// Database connection (will fail but server continues)
db.connect((err) => {
  if (err) {
    console.error('Database connection failed:', err);
    console.log('Running in memory-only mode with default admin user');
    return;
  }
  console.log('Connected to MySQL database');
});

// JWT middleware for protected routes
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Basic route
app.get('/', (req, res) => {
  res.json({
    message: 'Inventory Management System API',
    status: 'running',
    version: '1.0.0'
  });
});

// Authentication Routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, password, role = 'employee' } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Check if user already exists
    const existingUser = users.find(user => user.username === username);
    if (existingUser) {
      return res.status(400).json({ error: 'Username already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = {
      id: nextUserId++,
      username,
      password: hashedPassword,
      role,
      created_at: new Date()
    };

    users.push(newUser);

    res.status(201).json({
      message: 'User created successfully',
      userId: newUser.id
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Find user
    const user = users.find(user => user.username === username);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        role: user.role
      },
      process.env.JWT_SECRET || 'fallback_secret',
      { expiresIn: '24h' }
    );

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        username: user.username,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Protected route example
app.get('/api/auth/profile', authenticateToken, (req, res) => {
  res.json({ user: req.user });
});

// Product Management Routes
app.get('/api/products', authenticateToken, (req, res) => {
  try {
    // Add supplier names to products
    const productsWithSuppliers = products.map(product => {
      const supplier = suppliers.find(s => s.id === product.supplier_id);
      return {
        ...product,
        supplier_name: supplier ? supplier.name : null
      };
    });

    res.json(productsWithSuppliers);
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

app.post('/api/products', authenticateToken, (req, res) => {
  try {
    const { name, description, category, price, quantity, supplier_id, expiry_date } = req.body;

    if (!name || !price) {
      return res.status(400).json({ error: 'Name and price are required' });
    }

    // Create new product
    const newProduct = {
      id: nextProductId++,
      name,
      description: description || '',
      category: category || '',
      price: parseFloat(price),
      quantity: parseInt(quantity) || 0,
      supplier_id: supplier_id || null,
      expiry_date: expiry_date || null,
      created_at: new Date()
    };

    products.push(newProduct);

    res.status(201).json({
      message: 'Product created successfully',
      productId: newProduct.id
    });
  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
});

app.put('/api/products/:id', authenticateToken, (req, res) => {
  const { id } = req.params;
  const { name, description, category, price, quantity, supplier_id, expiry_date } = req.body;

  if (!name || !price) {
    return res.status(400).json({ error: 'Name and price are required' });
  }

  // Get current quantity for stock movement tracking
  db.query('SELECT quantity FROM products WHERE id = ?', [id], (err, results) => {
    if (err || results.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    const currentQuantity = results[0].quantity;
    const quantityChange = quantity - currentQuantity;

    const query = `
      UPDATE products
      SET name = ?, description = ?, category = ?, price = ?, quantity = ?, supplier_id = ?, expiry_date = ?
      WHERE id = ?
    `;

    db.query(query, [name, description, category, price, quantity, supplier_id, expiry_date, id], (err) => {
      if (err) {
        return res.status(500).json({ error: 'Failed to update product' });
      }

      // Log stock movement if quantity changed
      if (quantityChange !== 0) {
        const stockQuery = `
          INSERT INTO stock_movements (product_id, quantity_change, movement_type, reason)
          VALUES (?, ?, ?, 'Stock adjustment')
        `;
        const movementType = quantityChange > 0 ? 'in' : 'out';
        db.query(stockQuery, [id, Math.abs(quantityChange), movementType]);
      }

      res.json({ message: 'Product updated successfully' });
    });
  });
});

app.delete('/api/products/:id', authenticateToken, (req, res) => {
  const { id } = req.params;

  // Check if user is admin
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }

  db.query('DELETE FROM products WHERE id = ?', [id], (err, result) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to delete product' });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({ message: 'Product deleted successfully' });
  });
});

// Supplier Management Routes
app.get('/api/suppliers', authenticateToken, (req, res) => {
  try {
    res.json(suppliers);
  } catch (error) {
    console.error('Get suppliers error:', error);
    res.status(500).json({ error: 'Failed to fetch suppliers' });
  }
});

app.post('/api/suppliers', authenticateToken, (req, res) => {
  try {
    const { name, contact_person, email, phone, address } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Supplier name is required' });
    }

    const newSupplier = {
      id: nextSupplierId++,
      name,
      contact_person: contact_person || '',
      email: email || '',
      phone: phone || '',
      address: address || '',
      created_at: new Date()
    };

    suppliers.push(newSupplier);

    res.status(201).json({
      message: 'Supplier created successfully',
      supplierId: newSupplier.id
    });
  } catch (error) {
    console.error('Create supplier error:', error);
    res.status(500).json({ error: 'Failed to create supplier' });
  }
});

// Get all users (admin only)
app.get('/api/users', authenticateToken, (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const userList = users.map(user => ({
      id: user.id,
      username: user.username,
      role: user.role,
      email: user.email,
      created_at: user.created_at
    }));

    res.json(userList);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Categories Management Routes
app.get('/api/categories', authenticateToken, (req, res) => {
  try {
    res.json(categories);
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

app.post('/api/categories', authenticateToken, (req, res) => {
  try {
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Category name is required' });
    }

    // Check if category already exists
    const existingCategory = categories.find(cat => cat.name.toLowerCase() === name.toLowerCase());
    if (existingCategory) {
      return res.status(400).json({ error: 'Category already exists' });
    }

    const newCategory = {
      id: nextCategoryId++,
      name,
      description: description || '',
      created_at: new Date()
    };

    categories.push(newCategory);

    res.status(201).json({
      message: 'Category created successfully',
      categoryId: newCategory.id
    });
  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({ error: 'Failed to create category' });
  }
});

// Customers Management Routes
app.get('/api/customers', authenticateToken, (req, res) => {
  try {
    res.json(customers);
  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({ error: 'Failed to fetch customers' });
  }
});

app.post('/api/customers', authenticateToken, (req, res) => {
  try {
    const { name, email, phone, address, gst_number } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Customer name is required' });
    }

    const newCustomer = {
      id: nextCustomerId++,
      name,
      email: email || '',
      phone: phone || '',
      address: address || '',
      gst_number: gst_number || '',
      created_at: new Date()
    };

    customers.push(newCustomer);

    res.status(201).json({
      message: 'Customer created successfully',
      customerId: newCustomer.id
    });
  } catch (error) {
    console.error('Create customer error:', error);
    res.status(500).json({ error: 'Failed to create customer' });
  }
});

// Dashboard Analytics Routes
app.get('/api/dashboard/stats', authenticateToken, (req, res) => {
  try {
    const totalProducts = products.length;
    const totalStock = products.reduce((sum, product) => sum + (product.quantity || 0), 0);
    const lowStockItems = products.filter(product => (product.quantity || 0) < settings.low_stock_threshold);
    const expiringSoonItems = products.filter(product => {
      if (!product.expiry_date) return false;
      const expiryDate = new Date(product.expiry_date);
      const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
      return expiryDate <= thirtyDaysFromNow;
    });

    const totalInvoices = invoices.length;
    const totalRevenue = invoices.reduce((sum, invoice) => sum + (invoice.total_amount || 0), 0);

    // Recent invoices (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentInvoices = invoices.filter(invoice => new Date(invoice.created_at) >= sevenDaysAgo);

    res.json({
      totalProducts,
      totalStock,
      lowStockCount: lowStockItems.length,
      expiringSoonCount: expiringSoonItems.length,
      totalInvoices,
      totalRevenue,
      recentInvoicesCount: recentInvoices.length,
      lowStockItems: lowStockItems.slice(0, 5), // Top 5 low stock items
      expiringSoonItems: expiringSoonItems.slice(0, 5), // Top 5 expiring items
      recentInvoices: recentInvoices.slice(0, 5) // Last 5 invoices
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard statistics' });
  }
});

// Invoice/Billing Routes
app.post('/api/invoices', authenticateToken, (req, res) => {
  try {
    const { customer_id, items, discount_amount = 0, tax_rate = settings.tax_rate } = req.body;

    if (!items || items.length === 0) {
      return res.status(400).json({ error: 'Invoice must contain at least one item' });
    }

    // Calculate totals
    let subtotal = 0;
    const processedItems = [];

    for (const item of items) {
      const product = products.find(p => p.id === item.product_id);
      if (!product) {
        return res.status(400).json({ error: `Product with ID ${item.product_id} not found` });
      }

      if (product.quantity < item.quantity) {
        return res.status(400).json({ error: `Insufficient stock for ${product.name}. Available: ${product.quantity}` });
      }

      const itemTotal = item.quantity * item.unit_price;
      subtotal += itemTotal;

      processedItems.push({
        product_id: item.product_id,
        product_name: product.name,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: itemTotal
      });
    }

    const discountAmount = discount_amount || 0;
    const taxAmount = (subtotal - discountAmount) * (tax_rate / 100);
    const totalAmount = subtotal - discountAmount + taxAmount;

    // Generate invoice number
    const invoiceNumber = `${settings.invoice_prefix}${String(nextInvoiceId).padStart(6, '0')}`;

    // Create invoice
    const newInvoice = {
      id: nextInvoiceId++,
      invoice_number: invoiceNumber,
      customer_id: customer_id || null,
      user_id: req.user.userId,
      subtotal,
      tax_amount: taxAmount,
      discount_amount: discountAmount,
      total_amount: totalAmount,
      payment_method: req.body.payment_method || 'cash',
      status: 'paid',
      created_at: new Date(),
      items: processedItems
    };

    invoices.push(newInvoice);

    // Update product quantities and create stock movements
    for (const item of items) {
      const product = products.find(p => p.id === item.product_id);
      product.quantity -= item.quantity;

      // Log stock movement
      stockMovements.push({
        id: nextStockMovementId++,
        product_id: item.product_id,
        quantity_change: -item.quantity,
        movement_type: 'out',
        reason: `Sale - Invoice ${invoiceNumber}`,
        user_id: req.user.userId,
        created_at: new Date()
      });
    }

    res.status(201).json({
      message: 'Invoice created successfully',
      invoice: newInvoice
    });
  } catch (error) {
    console.error('Create invoice error:', error);
    res.status(500).json({ error: 'Failed to create invoice' });
  }
});

app.get('/api/invoices', authenticateToken, (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;

    let filteredInvoices = invoices;

    // Search functionality
    if (search) {
      filteredInvoices = invoices.filter(invoice =>
        invoice.invoice_number.toLowerCase().includes(search.toLowerCase()) ||
        (invoice.customer_id && customers.find(c => c.id === invoice.customer_id)?.name.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedInvoices = filteredInvoices.slice(startIndex, endIndex);

    // Add customer details
    const invoicesWithDetails = paginatedInvoices.map(invoice => {
      const customer = customers.find(c => c.id === invoice.customer_id);
      const user = users.find(u => u.id === invoice.user_id);

      return {
        ...invoice,
        customer_name: customer ? customer.name : 'Walk-in Customer',
        user_name: user ? user.username : 'Unknown'
      };
    });

    res.json({
      invoices: invoicesWithDetails,
      total: filteredInvoices.length,
      page: parseInt(page),
      totalPages: Math.ceil(filteredInvoices.length / limit)
    });
  } catch (error) {
    console.error('Get invoices error:', error);
    res.status(500).json({ error: 'Failed to fetch invoices' });
  }
});

// Search Products Route
app.get('/api/products/search', authenticateToken, (req, res) => {
  try {
    const { q = '', category = '', supplier_id = '', low_stock = false } = req.query;

    let filteredProducts = products;

    // Text search
    if (q) {
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(q.toLowerCase()) ||
        product.description.toLowerCase().includes(q.toLowerCase()) ||
        (product.barcode && product.barcode.includes(q))
      );
    }

    // Category filter
    if (category) {
      filteredProducts = filteredProducts.filter(product =>
        product.category.toLowerCase() === category.toLowerCase()
      );
    }

    // Supplier filter
    if (supplier_id) {
      filteredProducts = filteredProducts.filter(product =>
        product.supplier_id === parseInt(supplier_id)
      );
    }

    // Low stock filter
    if (low_stock === 'true') {
      filteredProducts = filteredProducts.filter(product =>
        (product.quantity || 0) < settings.low_stock_threshold
      );
    }

    // Add supplier names
    const productsWithSuppliers = filteredProducts.map(product => {
      const supplier = suppliers.find(s => s.id === product.supplier_id);
      return {
        ...product,
        supplier_name: supplier ? supplier.name : null
      };
    });

    res.json(productsWithSuppliers);
  } catch (error) {
    console.error('Search products error:', error);
    res.status(500).json({ error: 'Failed to search products' });
  }
});

// Settings Routes
app.get('/api/settings', authenticateToken, (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    res.json(settings);
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({ error: 'Failed to fetch settings' });
  }
});

app.put('/api/settings', authenticateToken, (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const allowedSettings = [
      'company_name', 'company_address', 'company_phone', 'company_email',
      'gst_number', 'currency_symbol', 'tax_rate', 'low_stock_threshold', 'invoice_prefix'
    ];

    for (const [key, value] of Object.entries(req.body)) {
      if (allowedSettings.includes(key)) {
        settings[key] = value;
      }
    }

    res.json({ message: 'Settings updated successfully', settings });
  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({ error: 'Failed to update settings' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API Documentation available at http://localhost:${PORT}/api`);
});