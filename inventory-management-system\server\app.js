const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const mysql = require('mysql2');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Database connection
const db = mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'inventory_db'
});

// In-memory user storage (since we don't have MySQL)
let users = [];
let products = [];
let suppliers = [];
let nextUserId = 1;
let nextProductId = 1;
let nextSupplierId = 1;

// Create default admin user
const createDefaultAdmin = async () => {
  const hashedPassword = await bcrypt.hash('admin123', 10);
  users.push({
    id: nextUserId++,
    username: 'admin',
    password: hashedPassword,
    role: 'admin',
    created_at: new Date()
  });
  console.log('Default admin user created: username=admin, password=admin123');
};

// Initialize default data
createDefaultAdmin();

// Database connection (will fail but server continues)
db.connect((err) => {
  if (err) {
    console.error('Database connection failed:', err);
    console.log('Running in memory-only mode with default admin user');
    return;
  }
  console.log('Connected to MySQL database');
});

// JWT middleware for protected routes
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Basic route
app.get('/', (req, res) => {
  res.json({
    message: 'Inventory Management System API',
    status: 'running',
    version: '1.0.0'
  });
});

// Authentication Routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, password, role = 'employee' } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Check if user already exists
    const existingUser = users.find(user => user.username === username);
    if (existingUser) {
      return res.status(400).json({ error: 'Username already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = {
      id: nextUserId++,
      username,
      password: hashedPassword,
      role,
      created_at: new Date()
    };

    users.push(newUser);

    res.status(201).json({
      message: 'User created successfully',
      userId: newUser.id
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Find user
    const user = users.find(user => user.username === username);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        role: user.role
      },
      process.env.JWT_SECRET || 'fallback_secret',
      { expiresIn: '24h' }
    );

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        username: user.username,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Protected route example
app.get('/api/auth/profile', authenticateToken, (req, res) => {
  res.json({ user: req.user });
});

// Product Management Routes
app.get('/api/products', authenticateToken, (req, res) => {
  try {
    // Add supplier names to products
    const productsWithSuppliers = products.map(product => {
      const supplier = suppliers.find(s => s.id === product.supplier_id);
      return {
        ...product,
        supplier_name: supplier ? supplier.name : null
      };
    });

    res.json(productsWithSuppliers);
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

app.post('/api/products', authenticateToken, (req, res) => {
  try {
    const { name, description, category, price, quantity, supplier_id, expiry_date } = req.body;

    if (!name || !price) {
      return res.status(400).json({ error: 'Name and price are required' });
    }

    // Create new product
    const newProduct = {
      id: nextProductId++,
      name,
      description: description || '',
      category: category || '',
      price: parseFloat(price),
      quantity: parseInt(quantity) || 0,
      supplier_id: supplier_id || null,
      expiry_date: expiry_date || null,
      created_at: new Date()
    };

    products.push(newProduct);

    res.status(201).json({
      message: 'Product created successfully',
      productId: newProduct.id
    });
  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
});

app.put('/api/products/:id', authenticateToken, (req, res) => {
  const { id } = req.params;
  const { name, description, category, price, quantity, supplier_id, expiry_date } = req.body;

  if (!name || !price) {
    return res.status(400).json({ error: 'Name and price are required' });
  }

  // Get current quantity for stock movement tracking
  db.query('SELECT quantity FROM products WHERE id = ?', [id], (err, results) => {
    if (err || results.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    const currentQuantity = results[0].quantity;
    const quantityChange = quantity - currentQuantity;

    const query = `
      UPDATE products
      SET name = ?, description = ?, category = ?, price = ?, quantity = ?, supplier_id = ?, expiry_date = ?
      WHERE id = ?
    `;

    db.query(query, [name, description, category, price, quantity, supplier_id, expiry_date, id], (err) => {
      if (err) {
        return res.status(500).json({ error: 'Failed to update product' });
      }

      // Log stock movement if quantity changed
      if (quantityChange !== 0) {
        const stockQuery = `
          INSERT INTO stock_movements (product_id, quantity_change, movement_type, reason)
          VALUES (?, ?, ?, 'Stock adjustment')
        `;
        const movementType = quantityChange > 0 ? 'in' : 'out';
        db.query(stockQuery, [id, Math.abs(quantityChange), movementType]);
      }

      res.json({ message: 'Product updated successfully' });
    });
  });
});

app.delete('/api/products/:id', authenticateToken, (req, res) => {
  const { id } = req.params;

  // Check if user is admin
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }

  db.query('DELETE FROM products WHERE id = ?', [id], (err, result) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to delete product' });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({ message: 'Product deleted successfully' });
  });
});

// Supplier Management Routes
app.get('/api/suppliers', authenticateToken, (req, res) => {
  try {
    res.json(suppliers);
  } catch (error) {
    console.error('Get suppliers error:', error);
    res.status(500).json({ error: 'Failed to fetch suppliers' });
  }
});

app.post('/api/suppliers', authenticateToken, (req, res) => {
  try {
    const { name, contact_person, email, phone, address } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Supplier name is required' });
    }

    const newSupplier = {
      id: nextSupplierId++,
      name,
      contact_person: contact_person || '',
      email: email || '',
      phone: phone || '',
      address: address || '',
      created_at: new Date()
    };

    suppliers.push(newSupplier);

    res.status(201).json({
      message: 'Supplier created successfully',
      supplierId: newSupplier.id
    });
  } catch (error) {
    console.error('Create supplier error:', error);
    res.status(500).json({ error: 'Failed to create supplier' });
  }
});

// Get all users (admin only)
app.get('/api/users', authenticateToken, (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const userList = users.map(user => ({
      id: user.id,
      username: user.username,
      role: user.role,
      created_at: user.created_at
    }));

    res.json(userList);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});