# Shop Inventory Management System

This is a full-stack inventory management application built with React, Node.js, Express, and MySQL.

## Features
- User authentication (Admin/Employee roles)
- Product management
- Stock tracking with alerts
- Sales and billing module
- Supplier management
- Reporting and analytics

## Tech Stack
- **Frontend**: React with Tailwind CSS
- **Backend**: Node.js/Express
- **Database**: MySQL
- **Authentication**: JWT

## Setup Instructions

### Prerequisites
- Node.js v18+
- MySQL 8.0+
- npm

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/inventory-management-system.git
   cd inventory-management-system
   ```

2. **Set up the database**
   - Create a MySQL database named `inventory_db`
   - Import the schema:
     ```bash
     mysql -u root -p inventory_db < database/schema.sql
     ```

3. **Backend Setup**
   ```bash
   cd server
   npm install
   ```

   Create a `.env` file with:
   ```
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=yourpassword
   DB_NAME=inventory_db
   JWT_SECRET=your_jwt_secret
   ```

4. **Frontend Setup**
   ```bash
   cd ../client
   npm install
   ```

5. **Run the application**
   - Start backend:
     ```bash
     cd server
     npm start
     ```
   - Start frontend:
     ```bash
     cd client
     npm start
     ```

6. **Access the application**
   Open http://localhost:3000 in your browser

## Screenshots
![Dashboard](screenshots/dashboard.png)

## License
MIT